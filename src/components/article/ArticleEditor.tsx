"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, TextField, FormControlLabel, Switch, FormHelperText, Box, Typography, Chip, IconButton, Checkbox } from "@mui/material";
import { FaImage, FaLock, FaTimes, FaFileContract, FaExternalLinkAlt } from "react-icons/fa";
import { useFormik } from "formik";
import * as yup from "yup";
import { getFullURL, uploadMedia } from "@/utilities/misc/getFullURL";
import { useToast } from "@/components/ui/use-toast";
import { useLanguage } from "@/hooks/useLanguage";
import { useIsMobile } from "@/hooks/useIsMobile";
import { cn } from "@/lib/utils";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import RichTextEditor from "@/components/editor/RichTextEditor";
import CreatorAgreement from "./CreatorAgreement";

// 文章编辑器属性
interface ArticleEditorProps {
  initialValues?: {
    articleId?: string;
    title: string;
    content: string;
    summary?: string;
    coverImage?: string | null;
    tags?: string[];
    isHidden?: boolean;
    warningText?: string;
    visibility?: "public" | "onlyLoggedIn";
    contentType?: "original" | "distribution" | "translation";
    originalAuthor?: string;
    originalSource?: string;
    translationPermission?: string;
    applyForIncentive?: boolean;
  };
  onSubmit: (values: any) => Promise<boolean>;
  isEdit?: boolean;
}

export default function ArticleEditor({ initialValues, onSubmit, isEdit = false }: ArticleEditorProps) {
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(initialValues?.coverImage ? getFullURL(initialValues?.coverImage) : null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tags, setTags] = useState<string[]>(initialValues?.tags || []);
  const [tagInput, setTagInput] = useState("");
  const [showAgreement, setShowAgreement] = useState(false);
  const [currentAgreementType, setCurrentAgreementType] = useState<'base' | 'incentive' | 'free'>('base');
  const [agreementAccepted, setAgreementAccepted] = useState(isEdit); // 编辑模式下默认已接受
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { t, language } = useLanguage();
  const isMobile = useIsMobile();

  // 检查是否为编辑模式且原本申请了创作激励
  const isEditWithIncentive = isEdit && initialValues?.applyForIncentive === true;

  // 可见性选项
  const visibilityOptions = {
    public: {
      label: t("article.visibilityPublic"),
      icon: <FaEye className="h-4 w-4" />,
      description: t("article.visibilityPublicDesc"),
    },
    onlyLoggedIn: {
      label: t("article.visibilityLoggedIn"),
      icon: <FaLock className="h-4 w-4" />,
      description: t("article.visibilityLoggedInDesc"),
    },
  };

  // 内容类型选项
  const contentTypeOptions = {
    original: {
      label: t("article.contentTypeOriginal"),
      description: t("article.contentTypeOriginalDesc"),
    },
    distribution: {
      label: t("article.contentTypeDistribution"),
      description: t("article.contentTypeDistributionDesc"),
    },
    translation: {
      label: t("article.contentTypeTranslation"),
      description: t("article.contentTypeTranslationDesc"),
    },
  };

  // 表单验证
  const validationSchema = yup.object({
    title: yup
      .string()
      .min(5, t("article.titleMinLength"))
      .max(30, t("article.titleMaxLength"))
      .required(t("article.titleRequired")),
    content: yup
      .string()
      .min(300, t("article.contentMinLength"))
      .required(t("article.contentRequired")),
    summary: yup
      .string()
      .max(200, t("article.summaryMaxLength")),
    warningText: yup
      .string()
      .max(50, t("article.warningMaxLength"))
      .when("isHidden", {
        is: true,
        then: (schema) => schema.required(t("article.warningRequired")),
      }),
    tags: yup.array().of(yup.string().max(20, t("article.tagMaxLength"))).max(5, t("article.tagMaxCount")),
    originalAuthor: yup
      .string()
      .when("contentType", {
        is: "translation",
        then: (schema) => schema.required(t("article.translationAuthRequired")),
      }),
    originalSource: yup
      .string()
      .when("contentType", {
        is: "translation",
        then: (schema) => schema.required(t("article.translationAuthRequired")),
      }),
    translationPermission: yup
      .string()
      .when("contentType", {
        is: "translation",
        then: (schema) => schema.required(t("article.translationAuthRequired")),
      }),
  });

  // 初始化表单
  const formik = useFormik({
    initialValues: initialValues || {
      title: "",
      content: "",
      summary: "",
      coverImage: null,
      tags: [],
      isHidden: false,
      warningText: "",
      visibility: "public" as "public" | "onlyLoggedIn",
      contentType: "original" as "original" | "distribution" | "translation",
      originalAuthor: "",
      originalSource: "",
      translationPermission: "",
      applyForIncentive: true,
    },
    validationSchema,
    onSubmit: async (values) => {
      // 如果是新建文章且未接受协议，显示协议对话框
      if (!isEdit && !agreementAccepted) {
        setShowAgreement(true);
        return;
      }

      setIsSubmitting(true);
      try {
        // 处理封面图片上传
        if (coverImageFile) {
          // 只有在选择了新文件时才上传
          const path: string | void = await uploadMedia(coverImageFile);
          if (!path) throw new Error(t("article.coverImageUploadFailed"));
          values.coverImage = path;
        } else if (coverImagePreview && !coverImageFile) {
          // 如果有预览图但没有新文件，说明是编辑模式下保持原有封面
          // 保持原有的 coverImage 值不变
          values.coverImage = initialValues?.coverImage || null;
        } else {
          // 没有封面图片
          values.coverImage = null;
        }

        // 提交表单
        const success = await onSubmit(values);
        if (success) {
          toast({
            title: isEdit ? t("article.updateSuccess") : t("article.publishSuccess"),
            description: isEdit ? t("article.updateSuccessDesc") : t("article.publishSuccessDesc"),
          });
          if (!isEdit) {
            // 重置表单
            formik.resetForm();
            setCoverImagePreview(null);
            setCoverImageFile(null);
            setTags([]);
            setAgreementAccepted(false); // 重置协议状态
          }
        } else {
          toast({
            title: t("article.operationFailed"),
            description: t("article.retryLater"),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("提交作品失败:", error);
        toast({
          title: t("article.operationFailed"),
          description: t("article.retryLater"),
          variant: "destructive",
        });
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  // 处理封面图片选择
  const handleCoverImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setCoverImageFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setCoverImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 移除封面图片
  const handleRemoveCoverImage = () => {
    setCoverImagePreview(null);
    setCoverImageFile(null);
    formik.setFieldValue("coverImage", null);
  };

  // 处理标签添加
  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && tagInput.trim()) {
      e.preventDefault();
      if (tags.length >= 5) {
        toast({
          title: t("article.tagLimitReached"),
          description: t("article.tagMaxCount"),
          variant: "destructive",
        });
        return;
      }
      if (!tags.includes(tagInput.trim())) {
        setTags([...tags, tagInput.trim()]);
      }
      setTagInput("");
    }
  };

  // 移除标签
  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  // 更新表单中的标签值
  useEffect(() => {
    formik.setFieldValue("tags", tags);
  }, [tags]);

  // 处理预览功能
  const handlePreview = () => {
    // 准备预览数据
    const previewData = {
      title: formik.values.title,
      content: formik.values.content,
      summary: formik.values.summary,
      coverImage: coverImagePreview, // 使用预览图片（可能是blob URL）
      tags: tags,
      isHidden: formik.values.isHidden,
      warningText: formik.values.warningText,
      visibility: formik.values.visibility,
      contentType: formik.values.contentType,
      originalAuthor: formik.values.originalAuthor,
      originalSource: formik.values.originalSource,
      translationPermission: formik.values.translationPermission,
      applyForIncentive: formik.values.applyForIncentive,
    };

    // 将数据存储到sessionStorage
    sessionStorage.setItem('articlePreviewData', JSON.stringify(previewData));

    // 在新窗口打开预览页面
    window.open('/preview', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
  };

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      {/* 标题 */}
      <div>
        <TextField
          fullWidth
          label={t("article.title")}
          name="title"
          value={formik.values.title}
          onChange={formik.handleChange}
          error={formik.touched.title && Boolean(formik.errors.title)}
          helperText={formik.touched.title && formik.errors.title}
          placeholder={t("article.titlePlaceholder")}
          variant="outlined"
          inputProps={{style: {fontSize: 16}}}
        />
      </div>

      {/* 摘要 */}
      <div>
        <TextField
          fullWidth
          label={t("article.summary")}
          name="summary"
          value={formik.values.summary}
          onChange={formik.handleChange}
          error={formik.touched.summary && Boolean(formik.errors.summary)}
          helperText={formik.touched.summary && formik.errors.summary}
          placeholder={t("article.summaryPlaceholder")}
          variant="outlined"
          multiline
          rows={2}
          inputProps={{style: {fontSize: 16}}}
        />
      </div>

      {/* 封面图片 */}
      <div>
        <Typography variant="subtitle1" gutterBottom>
          {t("article.coverImage")}
        </Typography>
        {coverImagePreview ? (
          <div className="relative w-full h-48 bg-gray-100 rounded-md overflow-hidden">
            <img
              src={coverImagePreview}
              alt={t("article.coverImagePreview")}
              className="w-full h-full object-cover"
            />
            <IconButton
              className="absolute top-2 right-2 bg-white/80 hover:bg-white"
              onClick={handleRemoveCoverImage}
              size="small"
            >
              <FaTimes />
            </IconButton>
          </div>
        ) : (
          <div
            className="w-full h-48 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="text-center">
              <FaImage className="mx-auto h-12 w-12 text-gray-400" />
              <Typography variant="body2" className="mt-2">
                {t("article.coverImageUpload")}
              </Typography>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleCoverImageChange}
              accept="image/*"
              className="hidden"
            />
          </div>
        )}
      </div>

      {/* 标签 */}
      <div>
        <Typography variant="subtitle1" gutterBottom>
          {t("article.tags")}
        </Typography>
        <div className="flex flex-wrap gap-2 mb-2">
          {tags.map((tag) => (
            <Chip
              key={tag}
              label={tag}
              onDelete={() => handleRemoveTag(tag)}
              color="primary"
              variant="outlined"
            />
          ))}
        </div>
        <TextField
          fullWidth
          placeholder={t("article.tagsPlaceholder")}
          value={tagInput}
          onChange={(e) => setTagInput(e.target.value)}
          onKeyDown={handleAddTag}
          variant="outlined"
          size="small"
          inputProps={{style: {fontSize: 16}}}
        />
      </div>

      {/* 富文本编辑器 */}
      <div>
        <Typography variant="subtitle1" gutterBottom>
          {t("article.content")}
        </Typography>
        <div className="border rounded-md overflow-hidden">
          <RichTextEditor
            value={formik.values.content}
            onChange={(value) => formik.setFieldValue("content", value)}
            height={500}
            placeholder={t("article.contentPlaceholder")}
          />
        </div>
        {formik.touched.content && formik.errors.content && (
          <FormHelperText error>{formik.errors.content}</FormHelperText>
        )}
      </div>

      {/* 文章设置 */}
      <div className="space-y-4">
        <Typography variant="subtitle1">{t("article.settings")}</Typography>

        {/* 内容类型设置 */}
        <div>
          <Typography variant="body2" className="mb-2 font-medium">
            {t("article.contentType")}
          </Typography>
          {isEditWithIncentive && (
            <Typography variant="caption" className="text-orange-600 dark:text-orange-400 mb-2 block">
              {t("article.incentiveContentTypeRestriction")}
            </Typography>
          )}
          <div className="flex flex-wrap gap-2">
            {Object.entries(contentTypeOptions).map(([key, option]) => (
              <Button
                key={key}
                variant={formik.values.contentType === key ? "contained" : "outlined"}
                size="small"
                className="flex-1 min-w-0"
                onClick={() => formik.setFieldValue("contentType", key)}
                disabled={isEditWithIncentive}
                sx={{
                  '& .MuiTypography-root': {
                    color: formik.values.contentType === key ? 'white' : 'primary.main'
                  }
                }}
              >
                <div className="text-center">
                  <Typography variant="body2" className="font-medium">{option.label}</Typography>
                  <Typography variant="caption" className="text-xs opacity-75">
                    {option.description}
                  </Typography>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* 创作激励选择 */}
        <div>
          <Typography variant="body2" className="mb-2 font-medium">
            {t("article.incentiveChoice")}
          </Typography>
          {isEditWithIncentive && (
            <Typography variant="caption" className="text-orange-600 dark:text-orange-400 mb-2 block">
              {t("article.incentiveChoiceRestriction")}
            </Typography>
          )}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={formik.values.applyForIncentive ? "contained" : "outlined"}
              size="small"
              className="flex-1"
              onClick={() => formik.setFieldValue("applyForIncentive", true)}
              disabled={isEditWithIncentive}
              sx={{
                '& .MuiTypography-root': {
                  color: formik.values.applyForIncentive ? 'white' : 'primary.main'
                }
              }}
            >
              <div className="text-center">
                <Typography variant="body2" className="font-medium">{t("article.applyForIncentive")}</Typography>
                <Typography variant="caption" className="text-xs opacity-75">
                  {t("article.applyForIncentiveDesc")}
                </Typography>
              </div>
            </Button>
            <Button
              variant={!formik.values.applyForIncentive ? "contained" : "outlined"}
              size="small"
              className="flex-1"
              onClick={() => formik.setFieldValue("applyForIncentive", false)}
              disabled={isEditWithIncentive}
              sx={{
                '& .MuiTypography-root': {
                  color: !formik.values.applyForIncentive ? 'white' : 'primary.main'
                }
              }}
            >
              <div className="text-center">
                <Typography variant="body2" className="font-medium">{t("article.noIncentiveMode")}</Typography>
                <Typography variant="caption" className="text-xs opacity-75">
                  {t("article.noIncentiveModeDesc")}
                </Typography>
              </div>
            </Button>
          </div>

          {/* 激励选择说明 */}
          <Box className={`mt-2 p-3 rounded-lg border ${
            formik.values.applyForIncentive
              ? 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-700'
              : 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700'
          }`}>
            <Typography variant="caption" className={
              formik.values.applyForIncentive
                ? 'text-orange-700 dark:text-orange-300'
                : 'text-green-700 dark:text-green-300'
            }>
              {formik.values.applyForIncentive ? t("article.incentiveAgreementNote") : t("article.freeAgreementNote")}
            </Typography>
          </Box>
        </div>

        {/* 翻译授权信息 - 仅在选择翻译内容时显示 */}
        {formik.values.contentType === "translation" && (
          <div className="space-y-4 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
            <Typography variant="subtitle2" className="font-medium text-orange-800 dark:text-orange-200">
              {t("article.translationAuth")}
            </Typography>
            {isEditWithIncentive && (
              <Typography variant="caption" className="text-orange-600 dark:text-orange-400 block">
                {t("article.incentiveTranslationRestriction")}
              </Typography>
            )}

            <TextField
              fullWidth
              label={t("article.originalAuthor")}
              name="originalAuthor"
              value={formik.values.originalAuthor}
              onChange={formik.handleChange}
              error={formik.touched.originalAuthor && Boolean(formik.errors.originalAuthor)}
              helperText={formik.touched.originalAuthor && formik.errors.originalAuthor}
              placeholder={t("article.originalAuthorPlaceholder")}
              variant="outlined"
              size="small"
              disabled={isEditWithIncentive}
              inputProps={{style: {fontSize: 16}}}
            />

            <TextField
              fullWidth
              label={t("article.originalSource")}
              name="originalSource"
              value={formik.values.originalSource}
              onChange={formik.handleChange}
              error={formik.touched.originalSource && Boolean(formik.errors.originalSource)}
              helperText={formik.touched.originalSource && formik.errors.originalSource}
              placeholder={t("article.originalSourcePlaceholder")}
              variant="outlined"
              size="small"
              disabled={isEditWithIncentive}
              inputProps={{style: {fontSize: 16}}}
            />

            <TextField
              fullWidth
              label={t("article.translationPermission")}
              name="translationPermission"
              value={formik.values.translationPermission}
              onChange={formik.handleChange}
              error={formik.touched.translationPermission && Boolean(formik.errors.translationPermission)}
              helperText={formik.touched.translationPermission && formik.errors.translationPermission}
              placeholder={t("article.translationPermissionPlaceholder")}
              variant="outlined"
              size="small"
              multiline
              rows={3}
              disabled={isEditWithIncentive}
              inputProps={{style: {fontSize: 16}}}
            />
          </div>
        )}

        {/* 可见性设置 */}
        <div>
          <Typography variant="body2" className="mb-2 font-medium">
            {t("article.visibility")}
          </Typography>
          <div className="flex flex-wrap gap-2">
            {Object.entries(visibilityOptions).map(([key, option]) => (
              <Button
                key={key}
                variant={formik.values.visibility === key ? "contained" : "outlined"}
                size="small"
                className="flex-1"
                startIcon={option.icon}
                onClick={() => formik.setFieldValue("visibility", key)}
                sx={{
                  '& .MuiTypography-root': {
                    color: formik.values.visibility === key ? 'white' : 'primary.main'
                  },
                  '& .MuiSvgIcon-root': {
                    color: formik.values.visibility === key ? 'white' : 'primary.main'
                  }
                }}
              >
                <div className="text-center">
                  <Typography variant="body2" className="font-medium">{option.label}</Typography>
                  <Typography variant="caption" className="text-xs opacity-75">
                    {option.description}
                  </Typography>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* 内容警告设置 */}
        <div>
          <FormControlLabel
            control={
              <Switch
                checked={formik.values.isHidden}
                onChange={(e) => {
                  formik.setFieldValue("isHidden", e.target.checked);
                }}
                name="isHidden"
              />
            }
            label={t("article.contentWarning")}
          />
          {formik.values.isHidden && (
            <TextField
              fullWidth
              label={t("article.warningText")}
              name="warningText"
              value={formik.values.warningText}
              onChange={formik.handleChange}
              error={formik.touched.warningText && Boolean(formik.errors.warningText)}
              helperText={formik.touched.warningText && formik.errors.warningText}
              placeholder={t("article.warningPlaceholder")}
              variant="outlined"
              size="small"
              sx={{
                mt: 2
              }}
              inputProps={{style: {fontSize: 16}}}
            />
          )}
        </div>
      </div>

      {/* 提交按钮 */}
      <div className="flex flex-col space-y-4">
        {/* 创作者协议提示 */}
        {!isEdit && (
          <Box className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <FormControlLabel
              control={
                <Checkbox
                  checked={agreementAccepted}
                  onChange={(e) => setAgreementAccepted(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Typography variant="body2" className="flex items-center gap-1 flex-wrap" sx={{ fontSize: '0.75rem' }}>
                  {t("article.agreeToTermsPrefix")}
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => {
                      setCurrentAgreementType('base');
                      setShowAgreement(true);
                    }}
                    className="text-blue-600 hover:bg-blue-100 dark:text-blue-400 dark:hover:bg-blue-900/50 p-1 min-w-auto"
                    sx={{ textTransform: 'none', fontSize: '0.75rem' }}
                  >
                    《{(() => {
                       // 第一个协议总是显示内容类型协议
                       switch (formik.values.contentType) {
                         case 'original':
                           return t("article.originalAgreementTitle");
                         case 'distribution':
                           return t("article.distributionAgreementTitle");
                         case 'translation':
                           return t("article.translationAgreementTitle");
                         default:
                           return t("article.originalAgreementTitle");
                       }
                     })()}》
                  </Button>
                  <>
                    <span>{language === 'zh' ? '和' : ' and '}</span>
                    <Button
                      variant="text"
                      size="small"
                      onClick={() => {
                        setCurrentAgreementType(formik.values.applyForIncentive ? 'incentive' : 'free');
                        setShowAgreement(true);
                      }}
                      className="text-blue-600 hover:bg-blue-100 dark:text-blue-400 dark:hover:bg-blue-900/50 p-1 min-w-auto"
                      sx={{ textTransform: 'none', fontSize: '0.75rem' }}
                    >
                      《{formik.values.applyForIncentive
                        ? t("article.incentiveAgreementTitle")
                        : t("article.freeAgreementTitle")
                      }》
                    </Button>
                  </>
                </Typography>
              }
            />
          </Box>
        )}

        {/* 添加错误信息 */}
        {formik.touched && !formik.isValid && (
          Object.keys(formik.errors)[0] && (
            <Typography variant="subtitle2" className="text-red-500 text-center">
              {formik.errors[Object.keys(formik.errors)[0] as keyof typeof formik.errors]}
            </Typography>
          )
        )}

        <div className="flex gap-3">
          <Button
            variant="outlined"
            color="primary"
            onClick={handlePreview}
            disabled={isSubmitting || !formik.values.title || !formik.values.content}
            startIcon={<FaExternalLinkAlt />}
            className="flex-1"
          >
            {t("article.previewInNewWindow")}
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={isSubmitting || !formik.isValid || (!isEdit && !agreementAccepted)}
            aria-label={isEdit ? t("article.update") : t("article.publish")}
            className="flex-1"
          >
            {isSubmitting ? t("article.submitting") : isEdit ? t("article.update") : t("article.publish")}
          </Button>
        </div>
      </div>

      {/* 创作者协议对话框 */}
      <CreatorAgreement
        open={showAgreement}
        onClose={() => setShowAgreement(false)}
        contentType={formik.values.contentType}
        applyForIncentive={formik.values.applyForIncentive}
        specificAgreement={currentAgreementType}
      />
    </form>
  );
}