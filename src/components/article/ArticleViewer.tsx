"use client";

import { useState } from "react";
import DOMPurify from "dompurify";
import { formatDateExtended } from "@/utilities/date";
import { <PERSON>ton, Chip, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Typography, Tooltip } from "@mui/material";
import { FaHeart, FaRegHeart, FaComment, FaEdit, FaTrash, FaEyeSlash, FaEye, FaDownload } from "react-icons/fa";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { CachedAvatar } from "@/components/common/CachedAvatar";
import { useLanguage } from "@/hooks/useLanguage";
import { exportArticleAsImage } from "@/utilities/imageExport";

// 文章查看器属性
interface ArticleViewerProps {
  article: {
    articleId: string;
    title: string;
    content: string;
    summary?: string | null;
    coverImage?: string | null;
    tags?: string[];
    createdAt: Date;
    updatedAt: Date;
    authorId: string;
    author: {
      id: string;
      username: string;
      photoUrl?: string | null;
      isPremium?: boolean;
    };
    isHidden?: boolean;
    warningText?: string | null;
    visibility: "public" | "onlyLoggedIn";
    stats?: {
      likes: number;
      comments: number;
      views: number;
      isLikedByMe: boolean;
    };
    // 新增字段
    contentType?: "original" | "distribution" | "translation";
    originalAuthor?: string | null;
    originalSource?: string | null;
    translationPermission?: string | null;
    applyForIncentive?: boolean;
  };
  currentUserId?: string;
  onLike?: (articleId: string, type: "like" | "unlike") => Promise<boolean>;
  onDelete?: (articleId: string) => Promise<boolean>;
}

export default function ArticleViewer({ article, currentUserId, onLike, onDelete }: ArticleViewerProps) {
  const [isLiked, setIsLiked] = useState(article.stats?.isLikedByMe || false);
  const [likesCount, setLikesCount] = useState(article.stats?.likes || 0);
  const [showContent, setShowContent] = useState(!article.isHidden);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const { t } = useLanguage();

  // 处理点赞
  const handleLike = async () => {
    if (!currentUserId) {
      toast({
        title: t("article.loginRequired"),
        description: t("article.loginRequiredDesc"),
        variant: "destructive",
      });
      return;
    }

    if (!onLike) return;

    const type = isLiked ? "unlike" : "like";
    const success = await onLike(article.articleId, type);

    if (success) {
      setIsLiked(!isLiked);
      setLikesCount(prev => isLiked ? prev - 1 : prev + 1);
    } else {
      toast({
        title: t("article.operationFailed"),
        description: t("article.retryLater"),
        variant: "destructive",
      });
    }
  };

  // 处理删除
  const handleDelete = async () => {
    if (!onDelete) return;

    try {
      const success = await onDelete(article.articleId);

      if (success) {
        toast({
          title: t("article.deleteSuccess"),
          description: t("article.deleteSuccessDesc"),
        });
        router.push("/articles");
      } else {
        toast({
          title: t("article.deleteFailed"),
          description: t("article.retryLater"),
          variant: "destructive",
        });
      }
    } catch (error: any) {
      // 检查是否是90天限制错误
      // axios错误格式: error.response.data.message 或 error.message
      const errorMessage = error.response?.data?.message || error.message || "";
      if (errorMessage.includes("90天内无法删除") || errorMessage.includes("90 days")) {
        toast({
          title: t("article.deleteIncentiveRestriction"),
          description: t("article.deleteIncentiveRestrictionDesc"),
          variant: "destructive",
        });
      } else {
        toast({
          title: t("article.deleteFailed"),
          description: t("article.retryLater"),
          variant: "destructive",
        });
      }
    }

    setDeleteDialogOpen(false);
  };

  // 处理编辑
  const handleEdit = () => {
    router.push(`/articles/edit/${article.articleId}`);
  };

  // 显示内容
  const handleShowContent = () => {
    setShowContent(true);
  };

  // 检查是否在激励删除限制期内
  const isInIncentiveRestrictionPeriod = () => {
    if (!article.applyForIncentive) return false;

    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    return new Date(article.createdAt) > ninetyDaysAgo;
  };

  // 处理保存为图片
  const handleSaveAsImage = async () => {
    if (isExporting) return;

    setIsExporting(true);
    try {
      toast({
        title: "正在生成图片...",
        description: "请稍等片刻",
      });

      const success = await exportArticleAsImage({
        title: article.title,
        content: article.content,
        summary: article.summary,
        warningText: article.warningText,
        tags: article.tags,
        author: {
          username: article.author.username,
          photoUrl: article.author.photoUrl,
        },
        createdAt: new Date(article.createdAt),
        contentType: article.contentType,
        originalAuthor: article.originalAuthor,
        originalSource: article.originalSource,
        translationPermission: article.translationPermission,
      });

      if (success) {
        toast({
          title: "导出成功 ✅",
          description: "图片已保存到下载文件夹",
        });
      } else {
        toast({
          title: "导出失败",
          description: "请稍后重试",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("保存图片失败:", error);
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };


  return (
    <div>
      {/* 文章头部 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">{article.title}</h1>

        {/* 作者信息和日期 */}
        <div className="flex items-center justify-between mb-4">
          <Link href={`/user/${article.author.id}`} className="flex items-center gap-2">
            <CachedAvatar
              photoUrl={article.author.photoUrl}
              className="w-10 h-10 rounded-full"
            />
            <div>
              <Typography variant="subtitle1" className="font-medium">
                {article.author.username}
                {article.author.isPremium && (
                  <span className="ml-1 text-yellow-500">✓</span>
                )}
              </Typography>
              <div className="flex items-center gap-2">
                <Typography variant="caption" className="text-gray-500">
                  {formatDateExtended(article.createdAt)}
                </Typography>
                {/* 创作激励计划标识 */}
                {article.applyForIncentive && (
                  <Chip
                    label={t("article.incentiveProgramBadge")}
                    size="small"
                    color="primary"
                    variant="filled"
                    className="text-xs"
                    sx={{
                      height: '20px',
                      fontSize: '0.65rem',
                      fontWeight: 'medium',
                      '& .MuiChip-label': {
                        paddingX: '6px'
                      }
                    }}
                  />
                )}
              </div>
            </div>
          </Link>

          {/* 可见性标识 */}
          <div className="flex items-center">
            {article.visibility === "onlyLoggedIn" && (
              <div className="flex items-center text-gray-500">
                <FaEyeSlash className="mr-1" />
                <Typography variant="caption">{t("article.onlyLoggedInVisible")}</Typography>
              </div>
            )}
          </div>
        </div>

        {/* 摘要 */}
        {article.summary && (
          <div className="mb-6 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <Typography variant="subtitle1" className="font-medium mb-2">
              {t("article.summary")}
            </Typography>
            <Typography variant="body2">{article.summary}</Typography>
          </div>
        )}

        {/* 标签 */}
        {article.tags && article.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-6">
            {article.tags.map((tag) => (
              <Link key={tag} href={`/articles?tag=${tag}`}>
                <Chip
                  label={`#${tag}`}
                  color="primary"
                  variant="outlined"
                  clickable
                />
              </Link>
            ))}
          </div>
        )}

        {/* 翻译授权信息 */}
        {article.contentType === "translation" && (
          <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <Typography variant="subtitle2" className="font-medium mb-3 text-blue-800 dark:text-blue-200">
              {t("article.translationInfo")}
            </Typography>
            <div className="space-y-2">
              {article.originalAuthor && (
                <div>
                  <Typography variant="body2" className="font-medium">{t("article.originalAuthorLabel")}</Typography>
                  <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                    {article.originalAuthor}
                  </Typography>
                </div>
              )}
              {article.originalSource && (
                <div>
                  <Typography variant="body2" className="font-medium">{t("article.originalSourceLabel")}</Typography>
                  <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                    {article.originalSource.startsWith('http') ? (
                      <a
                        href={article.originalSource}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {article.originalSource}
                      </a>
                    ) : (
                      article.originalSource
                    )}
                  </Typography>
                </div>
              )}
              {article.translationPermission && (
                <div>
                  <Typography variant="body2" className="font-medium">{t("article.translationPermissionLabel")}</Typography>
                  <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                    {article.translationPermission}
                  </Typography>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 内容警告 */}
      {article.isHidden && !showContent && (
        <div className="mb-8 bg-yellow-50 dark:bg-yellow-900/30 p-6 rounded-lg text-center">
          <Typography variant="h6" className="mb-2">
            {t("article.contentWarningTitle")}
          </Typography>
          <Typography variant="body1" className="mb-4">
            {article.warningText || t("article.contentWarningDefault")}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={handleShowContent}
          >
            {t("article.showContent")}
          </Button>
        </div>
      )}

      {/* 文章内容 */}
      {(!article.isHidden || showContent) && (
        <div className="mb-8">
          <div className="article-content">
            <div
              className="prose prose-sm md:prose-base lg:prose-lg max-w-none dark:prose-invert"
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(article.content)
              }}
            />
          </div>
        </div>
      )}

      {/* 文章底部操作栏 */}
      <div className="flex items-center justify-between border-t border-b py-4 mb-8">
        <div className="flex items-center gap-6">
          {/* 点赞按钮 */}
          <button
            className="flex items-center gap-1 text-gray-600 hover:text-red-500"
            onClick={handleLike}
          >
            {isLiked ? (
              <FaHeart className="text-red-500" />
            ) : (
              <FaRegHeart />
            )}
            <span>{likesCount}</span>
          </button>

          {/* 评论按钮 */}
          <Link href={`/articles/${article.articleId}#comments`} className="flex items-center gap-1 text-gray-600 hover:text-blue-500">
            <FaComment />
            <span>{article.stats?.comments || 0}</span>
          </Link>

          {/* 浏览量 */}
          <div className="flex items-center gap-1 text-gray-600">
            <FaEye />
            <span>{article.stats?.views || 0}</span>
          </div>

          {/* 保存为图片按钮 - 仅对公开内容或作者可见 */}
          {(article.visibility === "public" || currentUserId === article.authorId) && (
            <Tooltip title="保存为图片" arrow>
              <button
                className={`flex items-center gap-1 transition-colors duration-200 p-1 rounded-md ${
                  isExporting
                    ? 'text-green-500 bg-green-50 dark:bg-green-900/20 cursor-wait'
                    : 'text-gray-600 hover:text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20'
                }`}
                onClick={handleSaveAsImage}
                disabled={isExporting}
              >
                {isExporting ? (
                  <div className="animate-spin text-sm">⟳</div>
                ) : (
                  <FaDownload className="text-sm" />
                )}
                <span className="hidden sm:inline text-sm font-medium">
                  {isExporting ? '生成中...' : '保存为图片'}
                </span>
              </button>
            </Tooltip>
          )}
        </div>

        {/* 作者操作 */}
        {currentUserId === article.authorId && (
          <div className="flex items-center gap-2">
            <Button
              startIcon={<FaEdit />}
              variant="outlined"
              size="small"
              onClick={handleEdit}
            >
              {t("article.edit")}
            </Button>
            <Tooltip
              title={isInIncentiveRestrictionPeriod() ? t("article.deleteIncentiveRestrictionDesc") : ""}
              arrow
            >
              <span>
                <Button
                  startIcon={<FaTrash />}
                  variant="outlined"
                  color="error"
                  size="small"
                  disabled={isInIncentiveRestrictionPeriod()}
                  onClick={() => {
                    if (isInIncentiveRestrictionPeriod()) {
                      toast({
                        title: t("article.deleteIncentiveRestriction"),
                        description: t("article.deleteIncentiveRestrictionDesc"),
                        variant: "destructive",
                      });
                    } else {
                      setDeleteDialogOpen(true);
                    }
                  }}
                >
                  {t("article.delete")}
                </Button>
              </span>
            </Tooltip>
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t("article.deleteConfirm")}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t("article.deleteConfirmDesc")}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>{t("common.cancel")}</Button>
          <Button onClick={handleDelete} color="error" autoFocus>
            {t("article.delete")}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
