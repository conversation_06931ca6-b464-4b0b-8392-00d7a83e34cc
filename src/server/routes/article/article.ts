import { Hono } from "hono";
import { articles, articleLikes, articleComments, userProfiles, tweets, articleLogs, notifications } from "@/db/schema";
import { eq, and, desc, inArray, lt, notInArray, isNull, count, sql } from "drizzle-orm";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import {
    ArticleDetailResponseSchema,
    ArticleListResponseSchema,
    CreateArticleSchema,
    UpdateArticleSchema,
    ArticleLikeSchema,
} from "../../schemas/article";
import { keyBy } from "lodash";
import { queryInvisibleUserIds } from "../common";
import { getProfile } from "@/server/common";
import { db } from "@/db";
import { generateId } from "@/lib/idUtil";
import { ResultSchema } from "@/server/schemas/common";
import { createSnapshot } from "@/utilities/snapshot";

export const articleApi = new Hono();

// 获取文章列表
articleApi.get("/", async (c) => {
    const cursor = c.req.query("cursor") || "";
    const limit = parseInt(c.req.query("limit") || "10");
    const tag = c.req.query("tag") || [];

    let profile;
    try {
        profile = await getProfile(c);
    } catch (error) {
        // 未登录用户只能看到公开文章
        profile = null;
    }

    // 构建查询条件
    const conditions = [];

    // 如果指定了标签，则筛选数组中包含该标签的文章
    if (tag.length > 0) {
        conditions.push(sql`${articles.tags}@> ARRAY[${tag}::text]`);
    }

    // 未登录用户或查看他人文章时，只显示公开的文章
    if (!profile) {
        conditions.push(eq(articles.visibility, "public"));
    }

    // 如果有游标，添加游标条件
    if (cursor) {
        conditions.push(lt(articles.createdAt, new Date(cursor)));
    }

    // 如果用户已登录，排除被屏蔽用户的文章
    if (profile) {
        const blockedUserIds = await queryInvisibleUserIds(c, profile.id);
        if (blockedUserIds.size > 0) {
            conditions.push(notInArray(articles.authorId, Array.from(blockedUserIds)));
        }
    }

    // 执行查询
    const articlesQuery = await db()
        .select()
        .from(articles)
        .where(
            and(isNull(articles.deletedAt), conditions.length > 0 ? and(...conditions) : undefined)
        )
        .orderBy(desc(articles.createdAt))
        .limit(limit + 1);

    // 检查是否有更多数据
    const hasMore = articlesQuery.length > limit;
    if (hasMore) {
        articlesQuery.pop();
    }

    // 如果没有文章，直接返回空数组
    if (articlesQuery.length === 0) {
        return c.json(
            ArticleListResponseSchema.parse({
                success: true,
                data: [],
                hasMore: false,
                nextCursor: null,
            })
        );
    }

    // 获取作者信息
    const authorIds = articlesQuery.map((article) => article.authorId);
    const authorsQuery = await db()
        .select()
        .from(userProfiles)
        .where(inArray(userProfiles.id, authorIds));
    const authorsMap = keyBy(authorsQuery, "id");

    // 获取文章统计信息
    const articleIds = articlesQuery.map((article) => article.articleId);

    // 获取点赞数
    const likesCountQuery = await db()
        .select({
            articleId: articleLikes.articleId,
            count: count(),
        })
        .from(articleLikes)
        .where(and(inArray(articleLikes.articleId, articleIds), isNull(articleLikes.deletedAt)))
        .groupBy(articleLikes.articleId);

    const likesCountMap = keyBy(likesCountQuery, "articleId");

    // 获取评论数
    const commentsCountQuery = await db()
        .select({
            articleId: articleComments.articleId,
            count: count(),
        })
        .from(articleComments)
        .where(
            and(inArray(articleComments.articleId, articleIds), isNull(articleComments.deletedAt))
        )
        .groupBy(articleComments.articleId);

    const commentsCountMap = keyBy(commentsCountQuery, "articleId");

    // 获取浏览量
    const viewsCountQuery = await db()
        .select({
            articleId: articleLogs.articleId,
            count: count(),
        })
        .from(articleLogs)
        .where(inArray(articleLogs.articleId, articleIds))
        .groupBy(articleLogs.articleId);

    const viewsCountMap = keyBy(viewsCountQuery, "articleId");

    // 获取当前用户的点赞状态
    let userLikesMap: Record<string, boolean> = {};
    if (profile) {
        const userLikesQuery = await db()
            .select()
            .from(articleLikes)
            .where(
                and(
                    isNull(articleLikes.deletedAt),
                    inArray(articleLikes.articleId, articleIds),
                    eq(articleLikes.userId, profile.id)
                )
            );

        userLikesMap = userLikesQuery.reduce((acc: Record<string, boolean>, like) => {
            acc[like.articleId] = true;
            return acc;
        }, {});
    }

    // 组装文章数据
    const articlesData = articlesQuery.map((article) => {
        const author = authorsMap[article.authorId];
        const likesCount = likesCountMap[article.articleId]?.count || 0;
        const commentsCount = commentsCountMap[article.articleId]?.count || 0;
        const viewsCount = viewsCountMap[article.articleId]?.count || 0;
        const isLikedByMe = userLikesMap[article.articleId] || false;

        return {
            ...article,
            author,
            stats: {
                likes: likesCount,
                comments: commentsCount,
                views: viewsCount,
                isLikedByMe,
            },
        };
    });

    // 返回结果
    return c.json(
        ArticleListResponseSchema.parse({
            success: true,
            data: articlesData,
            hasMore,
            nextCursor: hasMore
                ? articlesQuery[articlesQuery.length - 1].createdAt.toISOString()
                : null,
        })
    );
});

// 获取文章详情
articleApi.get("/:id", async (c) => {
    const articleId = c.req.param("id");

    let profile;
    try {
        profile = await getProfile(c);
    } catch (error) {
        // 未登录用户只能看到公开文章
        profile = null;
    }

    // 获取文章
    const article = await existedArticle(articleId);

    // 检查权限
    if (article.visibility === "onlyLoggedIn" && (!profile || profile.id !== article.authorId)) {
        // 仅登录用户可见
        throw new HTTPException(403, { message: "无权查看该作品" });
    }

    // 获取作者信息
    const [author] = await db()
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.id, article.authorId));

    // 获取统计信息
    const [likesCount] = await db()
        .select({ count: count() })
        .from(articleLikes)
        .where(and(eq(articleLikes.articleId, articleId), isNull(articleLikes.deletedAt)));

    const [commentsCount] = await db()
        .select({ count: count() })
        .from(articleComments)
        .where(and(eq(articleComments.articleId, articleId), isNull(articleComments.deletedAt)));

    // 获取浏览量
    const [viewsCount] = await db()
        .select({ count: count() })
        .from(articleLogs)
        .where(eq(articleLogs.articleId, articleId));

    // 获取当前用户的点赞状态
    let isLikedByMe = false;
    if (profile) {
        const [userLike] = await db()
            .select()
            .from(articleLikes)
            .where(
                and(
                    eq(articleLikes.articleId, articleId),
                    eq(articleLikes.userId, profile.id),
                    isNull(articleLikes.deletedAt)
                )
            );

        isLikedByMe = !!userLike;
    }

    // 组装文章数据
    const articleData = {
        ...article,
        author,
        stats: {
            likes: likesCount?.count || 0,
            comments: commentsCount?.count || 0,
            views: viewsCount?.count || 0,
            isLikedByMe,
        },
    };

    // 返回结果
    return c.json(
        ArticleDetailResponseSchema.parse({
            success: true,
            data: articleData,
        })
    );
});

// 创建文章
articleApi.post("/create", async (c) => {
    const profile = await getProfile(c);

    const params = CreateArticleSchema.parse(await c.req.json());

    // 创建文章
    const [article] = await db()
        .insert(articles)
        .values({
            articleId: generateId(),
            title: params.title,
            content: params.content,
            summary: params.summary || null,
            coverImage: params.coverImage || null,
            tags: params.tags,
            authorId: profile.id,
            isHidden: params.isHidden || false,
            warningText: params.warningText || null,
            visibility: params.visibility,
            contentType: params.contentType || "original",
            originalAuthor: params.originalAuthor || null,
            originalSource: params.originalSource || null,
            translationPermission: params.translationPermission || null,
            applyForIncentive: params.applyForIncentive || false,
            createdAt: new Date(),
            updatedAt: new Date(),
        })
        .returning();

    createArticleVoice(article.articleId, article.authorId, article.title, article.coverImage);

    // 返回结果
    return c.json(
        ArticleDetailResponseSchema.parse({
            success: true,
            data: {
                ...article,
                author: profile,
                stats: {
                    likes: 0,
                    comments: 0,
                    views: 0,
                    isLikedByMe: false,
                },
            },
        })
    );
});

// 更新文章
articleApi.put("/update", async (c) => {
    const profile = await getProfile(c);

    const params = UpdateArticleSchema.parse(await c.req.json());

    await checkOperationUser(params.articleId, profile.id);

    // 获取更新前的文章数据用于快照
    const originalArticle = await existedArticle(params.articleId);

    // 检查是否申请了创作激励，如果是则禁止修改相关字段
    const isIncentiveArticle = originalArticle.applyForIncentive;

    // 如果原本申请了创作激励，检查是否尝试修改受限制的字段
    if (isIncentiveArticle) {
        if (params.contentType !== originalArticle.contentType) {
            throw new HTTPException(403, {
                message: "申请了创作激励的作品无法修改内容类型"
            });
        }

        if (params.applyForIncentive !== originalArticle.applyForIncentive) {
            throw new HTTPException(403, {
                message: "申请了创作激励的作品无法修改激励申请状态"
            });
        }

        // 如果是翻译类型，检查翻译授权信息是否被修改
        if (originalArticle.contentType === "translation") {
            if (params.originalAuthor !== originalArticle.originalAuthor ||
                params.originalSource !== originalArticle.originalSource ||
                params.translationPermission !== originalArticle.translationPermission) {
                throw new HTTPException(403, {
                    message: "申请了创作激励的翻译作品无法修改翻译授权信息"
                });
            }
        }
    }

    // 创建更新前快照
    try {
        await createSnapshot({
            tableName: 'articles',
            snapshotData: originalArticle,
            operationType: 'UPDATE',
            userId: profile.id,
            context: c,
        });
    } catch (error) {
        console.warn('Failed to create update snapshot:', error);
        // 快照失败不阻止更新操作，仅记录警告
    }

    // 准备更新数据，如果申请了创作激励则保持受限字段不变
    const updateData = {
        title: params.title,
        content: params.content,
        summary: params.summary || null,
        coverImage: params.coverImage || null,
        tags: params.tags,
        isHidden: params.isHidden || false,
        warningText: params.warningText || null,
        visibility: params.visibility,
        contentType: isIncentiveArticle ? originalArticle.contentType : (params.contentType || "original"),
        originalAuthor: isIncentiveArticle ? originalArticle.originalAuthor : (params.originalAuthor || null),
        originalSource: isIncentiveArticle ? originalArticle.originalSource : (params.originalSource || null),
        translationPermission: isIncentiveArticle ? originalArticle.translationPermission : (params.translationPermission || null),
        applyForIncentive: isIncentiveArticle ? originalArticle.applyForIncentive : (params.applyForIncentive || false),
        updatedAt: new Date(),
    };

    // 更新文章
    const [updatedArticle] = await db()
        .update(articles)
        .set(updateData)
        .where(eq(articles.articleId, params.articleId))
        .returning();

    // 获取统计信息
    const [likesCount] = await db()
        .select({ count: count() })
        .from(articleLikes)
        .where(and(eq(articleLikes.articleId, params.articleId), isNull(articleLikes.deletedAt)));

    const [commentsCount] = await db()
        .select({ count: count() })
        .from(articleComments)
        .where(
            and(eq(articleComments.articleId, params.articleId), isNull(articleComments.deletedAt))
        );

    // 获取浏览量
    const [viewsCount] = await db()
        .select({ count: count() })
        .from(articleLogs)
        .where(eq(articleLogs.articleId, params.articleId));

    // 获取当前用户的点赞状态
    const [userLike] = await db()
        .select()
        .from(articleLikes)
        .where(
            and(
                eq(articleLikes.articleId, params.articleId),
                eq(articleLikes.userId, profile.id),
                isNull(articleLikes.deletedAt)
            )
        );

    // 返回结果
    return c.json(
        ArticleDetailResponseSchema.parse({
            success: true,
            data: {
                ...updatedArticle,
                author: profile,
                stats: {
                    likes: likesCount?.count || 0,
                    comments: commentsCount?.count || 0,
                    views: viewsCount?.count || 0,
                    isLikedByMe: !!userLike,
                },
            },
        })
    );
});

// 删除文章
articleApi.delete("/:id", async (c) => {
    const articleId = c.req.param("id");
    const profile = await getProfile(c);

    // 检查文章是否存在并获取文章信息
    const article = await existedArticle(articleId);

    // 检查是否是文章作者
    if (article.authorId !== profile.id) {
        throw new HTTPException(403, { message: "无权操作该作品" });
    }

    // 创建删除前快照
    try {
        await createSnapshot({
            tableName: 'articles',
            snapshotData: article,
            operationType: 'DELETE',
            userId: profile.id,
            context: c,
        });
    } catch (error) {
        console.warn('Failed to create delete snapshot:', error);
        // 快照失败不阻止删除操作，仅记录警告
    }

    // 如果申请了激励，检查是否在90天内
    if (article.applyForIncentive) {
        const ninetyDaysAgo = new Date();
        ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

        if (article.createdAt > ninetyDaysAgo) {
            throw new HTTPException(403, {
                message: "申请了创作激励的作品在发布90天内无法删除"
            });
        }
    }

    // 删除文章相关的点赞
    db().update(articleLikes)
        .set({ deletedAt: new Date() })
        .where(eq(articleLikes.articleId, articleId));

    // 删除文章相关的评论
    db().update(articleComments)
        .set({ deletedAt: new Date() })
        .where(eq(articleComments.articleId, articleId));

    // 删除文章
    await db()
        .update(articles)
        .set({ deletedAt: new Date() })
        .where(eq(articles.articleId, articleId));

    await db()
        .delete(tweets)
        .where(eq(tweets.relatedId, articleId));

    // 返回结果
    return c.json(
        ResultSchema(z.null()).parse({
            success: true,
            data: null,
            message: "作品已删除",
        })
    );
});

// 点赞/取消点赞文章
articleApi.post("/like", async (c) => {
    const profile = await getProfile(c);

    const params = ArticleLikeSchema.parse(await c.req.json());

    const article = await existedArticle(params.articleId);

    if (params.type === "like") {
        // 检查是否已点赞
        const [existingLike] = await db()
            .select()
            .from(articleLikes)
            .where(
                and(
                    eq(articleLikes.articleId, params.articleId),
                    eq(articleLikes.userId, profile.id),
                    isNull(articleLikes.deletedAt)
                )
            );

        if (!existingLike) {
            // 添加点赞
            await db().insert(articleLikes).values({
                articleId: params.articleId,
                userId: profile.id,
                createdAt: new Date(),
            });

            // 如果不是自己的文章，创建通知
            if (article.authorId !== profile.id) {
                const notificationContent = {
                    sender: {
                        id: profile.id,
                        username: profile.username,
                        photoUrl: profile.photoUrl,
                    },
                    content: {
                        id: params.articleId,
                        title: article.title,
                        type: "article",
                    },
                };

                await db()
                    .insert(notifications)
                    .values({
                        id: generateId(),
                        userId: article.authorId,
                        type: "article_like",
                        content: JSON.stringify(notificationContent),
                        createdAt: new Date(),
                        isRead: false,
                    });
            }
        }
    } else {
        // 取消点赞
        await db()
            .update(articleLikes)
            .set({ deletedAt: new Date() })
            .where(
                and(
                    eq(articleLikes.articleId, params.articleId),
                    eq(articleLikes.userId, profile.id)
                )
            );
    }

    // 返回结果
    return c.json(
        ResultSchema(z.null()).parse({
            success: true,
            data: null,
            message: params.type === "like" ? "点赞成功" : "取消点赞成功",
        })
    );
});

// 记录文章浏览日志
articleApi.post('/:id/log-view', async (c) => {
    try {
        const { id: articleId } = c.req.param();

        let profile;
        try {
            profile = await getProfile(c);
        } catch (error) {
            // 未登录用户
            profile = null;
        }

        // 获取客户端IP地址
        const forwardedFor = c.req.header('x-forwarded-for');
        const realIp = c.req.header('x-real-ip');
        const remoteAddr = c.req.header('remote-addr');

        // 优先使用x-forwarded-for，然后是x-real-ip，最后是remote-addr
        let ipAddress = forwardedFor?.split(',')[0]?.trim() || realIp || remoteAddr || null;

        // 如果是IPv6的localhost，转换为IPv4
        if (ipAddress === '::1') {
            ipAddress = '127.0.0.1';
        }

        await db().insert(articleLogs).values({
            userId: profile?.id || null, // 如果用户未登录，userId为null
            articleId,
            ipAddress,
        });

        return c.json({
            success: true,
            message: ''
        });
    } catch (error) {
        console.error('记录文章浏览日志失败:', error);
        return c.json({
            success: false,
            message: ''
        }, 500);
    }
});

export const existedArticle = async (articleId: string) => {
    // 检查文章是否存在
    const [existingArticle] = await db()
        .select()
        .from(articles)
        .where(and(eq(articles.articleId, articleId), isNull(articles.deletedAt)));

    if (!existingArticle) {
        throw new HTTPException(404, { message: "作品不存在" });
    }

    return existingArticle;
};

export const checkOperationUser = async (articleId: string, profileId: string) => {
    // 检查是否是文章作者
    const article = await existedArticle(articleId);

    if (article.authorId !== profileId) {
        throw new HTTPException(403, { message: "无权操作该作品" });
    }
};

export const createArticleVoice = async (
    articleId: string,
    profileId: string,
    title: string,
    coverImage?: string | null
) => {
    await db()
        .insert(tweets)
        .values({
            id: generateId(),
            text: `我发布了作品《${title}》，快来看看吧！`,
            photoUrl: coverImage,
            authorId: profileId,
            createdAt: new Date(),
            type: "article",
            relatedId: articleId,
        });
};
