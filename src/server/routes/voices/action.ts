import { z } from "zod";
import { notifications, tweets, userLikes, userProfiles } from "@/db/schema";
import { UnauthorizedError } from "../../error/UnauthorizedError";
import { HTTPException } from "hono/http-exception";
import { Hono } from "hono";
import { getProfile, getSingleVoiceStats, getUserRealIP } from "../../common";
import { queryInvisibleUserIds } from "../common";
import { and, eq, desc, count } from "drizzle-orm";
import { PinnedResponseSchema } from "../../schemas/voiceType";
import { db } from "@/db";
import { generateId } from "@/lib/idUtil";
import { processMainVoice } from "@/server/common/activeOnlyUtils";
import { createSnapshot } from "@/utilities/snapshot";

export const voiceActionApi = new Hono();

const javaHost = process.env.JAVA_HOST!;

voiceActionApi.post("/like", async (c) => {
    const params = z
        .object({
            voiceId: z.string().min(1),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);
    const unviewedUserIds = await queryInvisibleUserIds(c, profile.id);
    const [voice] = await db().select().from(tweets).where(eq(tweets.id, params.voiceId));

    if (unviewedUserIds.has(voice.authorId)) {
        throw new UnauthorizedError("You are not authorized to perform this action.");
    }

    await db().insert(userLikes).values({
        voiceId: voice.id,
        userId: profile.id,
    });

    if (voice.authorId !== profile.id) {
        const notificationContent = {
            sender: {
                id: profile.id,
                username: profile.username,
                photoUrl: profile.photoUrl,
            },
            content: {
                id: voice.id,
            },
        };

        await db()
            .insert(notifications)
            .values({
                id: generateId(),
                userId: voice.authorId,
                type: "like",
                content: JSON.stringify(notificationContent),
            });
    }

    // 获取最新的统计数据
    const userIP = getUserRealIP(c);
    const likeCount = await fetch(`${javaHost}/api/voice/stat`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-Real-IP": userIP,
            "X-Forwarded-For": userIP,
        },
        body: JSON.stringify({
            voiceId: params.voiceId,
            type: "like",
        }),
    })
        .then((res) => res.json())
        .catch((err) => {
            console.error(err);
            return 0;
        });

    return c.json({
        success: true,
        data: {
            isLikedByMe: true,
            likeCount,
        },
    });
});

voiceActionApi.post("/unlike", async (c) => {
    const params = z
        .object({
            voiceId: z.string().min(1),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);

    await db()
        .delete(userLikes)
        .where(and(eq(userLikes.voiceId, params.voiceId), eq(userLikes.userId, profile.id)));

    const userIP2 = getUserRealIP(c);
    const likeCount = await fetch(`${javaHost}/api/voice/stat`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-Real-IP": userIP2,
            "X-Forwarded-For": userIP2,
        },
        body: JSON.stringify({
            voiceId: params.voiceId,
            type: "like",
        }),
    })
        .then((res) => res.json())
        .catch((err) => {
            console.error(err);
            return 0;
        });

    return c.json({
        success: true,
        data: {
            isLikedByMe: false,
            likeCount,
        },
    });
});

voiceActionApi.post("/reply", async (c) => {
    const params = z
        .object({
            voiceId: z.string().min(1),
            text: z.string(),
            photoUrl: z.string().nullable(),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);

    const userIP3 = getUserRealIP(c);
    const response = await fetch(`${javaHost}/api/reply`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-Real-IP": userIP3,
            "X-Forwarded-For": userIP3,
        },
        body: JSON.stringify({
            userId: profile.id,
            voiceId: params.voiceId,
            text: params.text,
            photoUrl: params.photoUrl,
        }),
    });

    if (!response.ok) {
        throw new Error("Failed to reply");
    }

    const result = await response.json();

    if (!result.ownVoice) {
        const notificationContent = {
            sender: {
                username: profile.username,
                id: profile.id,
                photoUrl: profile.photoUrl,
            },
            content: {
                id: result.repliedToId,
            },
            replyContent: {
                id: result.id,
                text: params.text,
                photoUrl: params.photoUrl,
            },
        };

        await db()
            .insert(notifications)
            .values({
                id: generateId(),
                userId: result.notificationUser,
                type: "reply",
                content: JSON.stringify(notificationContent),
                createdAt: new Date(result.createdAt),
                isRead: false,
            });
    }

    return c.json({ success: true });
});

voiceActionApi.get("/pinned/:id", async (c) => {
    const params = z
        .object({
            id: z.string().min(1),
        })
        .parse({
            id: c.req.param("id"),
        });

    const profile = await getProfile(c);

    const [pinned] = await db()
        .select()
        .from(tweets)
        .where(and(eq(tweets.authorId, params.id), eq(tweets.isPinned, true)))
        .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
        .orderBy(desc(tweets.createdAt))
        .limit(1);

    let stats = null;
    if (pinned) {
        stats = await getSingleVoiceStats(pinned.tweets.id, profile.id);
    }

    return c.json(
        PinnedResponseSchema.parse({
            success: true,
            data: pinned
                ? processMainVoice(pinned.tweets, pinned.user_profiles, stats, profile)
                : null,
        })
    );
});

voiceActionApi.post("/forward", async (c) => {
    const params = z
        .object({
            voiceId: z.string().min(1),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);
    const unviewedUserIds = await queryInvisibleUserIds(c, profile.id);

    const [voice] = await db().select().from(tweets).where(eq(tweets.id, params.voiceId));

    if (!voice || unviewedUserIds.has(voice.authorId)) {
        throw new HTTPException(404, { message: "Voice not found" });
    }

    const [forward] = await db()
        .insert(tweets)
        .values({
            id: generateId(),
            text: "",
            authorId: profile.id,
            isRetweet: true,
            retweetOfId: params.voiceId,
            createdAt: new Date(),
        })
        .returning();

    if (voice.authorId !== profile.id) {
        const notificationContent = {
            sender: {
                username: profile.username,
                id: profile.id,
                photoUrl: profile.photoUrl,
            },
            content: {
                id: params.voiceId,
            },
            replyContent: {
                id: forward.id,
                type: "voice",
            },
        };

        await db()
            .insert(notifications)
            .values({
                id: generateId(),
                userId: voice.authorId,
                type: "retweet",
                content: JSON.stringify(notificationContent),
                createdAt: new Date(forward.createdAt),
                isRead: false,
            });
    }

    // 获取最新的统计数据
    const userIP4 = getUserRealIP(c);
    const forwardedCount = await fetch(`${javaHost}/api/voice/stat`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-Real-IP": userIP4,
            "X-Forwarded-For": userIP4,
        },
        body: JSON.stringify({
            voiceId: params.voiceId,
            type: "forward",
        }),
    })
        .then((res) => res.json())
        .catch((err) => {
            console.error(err);
            return 0;
        });

    return c.json({
        success: true,
        data: {
            isRetweetedByMe: true,
            forwardedCount,
        },
    });
});

voiceActionApi.post("/forward-comment", async (c) => {
    const params = z
        .object({
            voiceId: z.string().min(1),
            comment: z.string().min(1),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);
    const unviewedUserIds = await queryInvisibleUserIds(c, profile.id);

    const [voice] = await db().select().from(tweets).where(eq(tweets.id, params.voiceId));

    if (!voice || unviewedUserIds.has(voice.authorId)) {
        throw new HTTPException(404, { message: "Voice not found" });
    }

    const [forward] = await db()
        .insert(tweets)
        .values({
            id: generateId(),
            text: params.comment,
            authorId: profile.id,
            isRetweet: true,
            retweetOfId: params.voiceId,
            createdAt: new Date(),
        })
        .returning();

    if (voice.authorId !== profile.id) {
        const notificationContent = {
            sender: {
                username: profile.username,
                id: profile.id,
                photoUrl: profile.photoUrl,
            },
            content: {
                id: params.voiceId,
            },
            replyContent: {
                id: forward.id,
                type: "voice",
            },
        };

        await db()
            .insert(notifications)
            .values({
                id: generateId(),
                userId: voice.authorId,
                type: "retweet",
                content: JSON.stringify(notificationContent),
                createdAt: new Date(forward.createdAt),
                isRead: false,
            });
    }

    // 获取最新的统计数据
    const userIP5 = getUserRealIP(c);
    const forwardedCount = await fetch(`${javaHost}/api/voice/stat`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-Real-IP": userIP5,
            "X-Forwarded-For": userIP5,
        },
        body: JSON.stringify({
            voiceId: params.voiceId,
            type: "forward",
        }),
    })
        .then((res) => res.json())
        .catch((err) => {
            console.error(err);
            return 0;
        });

    return c.json({
        success: true,
        data: {
            isRetweetedByMe: true,
            forwardedCount,
            retweetId: forward.id,
        },
    });
});

voiceActionApi.post("/unforward", async (c) => {
    const params = z
        .object({
            voiceId: z.string().min(1),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);

    await Promise.all([
        db()
            .delete(tweets)
            .where(
                and(
                    eq(tweets.retweetOfId, params.voiceId),
                    eq(tweets.authorId, profile.id),
                    eq(tweets.isRetweet, true)
                )
            ),
    ]);

    const userIP6 = getUserRealIP(c);
    const forwardedCount = await fetch(`${javaHost}/api/voice/stat`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-Real-IP": userIP6,
            "X-Forwarded-For": userIP6,
        },
        body: JSON.stringify({
            voiceId: params.voiceId,
            type: "forward",
        }),
    })
        .then((res) => res.json())
        .catch((err) => {
            console.error(err);
            return 0;
        });

    return c.json({
        success: true,
        data: {
            isRetweetedByMe: false,
            forwardedCount,
        },
    });
});

voiceActionApi.post("/pin", async (c) => {
    const params = z
        .object({
            voiceId: z.string().min(1),
            pinned: z.boolean(),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);

    if (params.pinned) {
        // 检查当前用户是否有已置顶的推文
        const [pinnedVoice] = await db()
            .select()
            .from(tweets)
            .where(and(eq(tweets.isPinned, true), eq(tweets.authorId, profile.id)));

        // 如果已经置顶了这条推文，直接返回成功
        if (pinnedVoice && pinnedVoice.id === params.voiceId) {
            return c.json({ success: true });
        }

        // 如果有其他置顶的推文，先取消置顶
        if (pinnedVoice) {
            await db()
                .update(tweets)
                .set({ isPinned: false })
                .where(and(eq(tweets.id, pinnedVoice.id), eq(tweets.authorId, profile.id)));
        }

        // 置顶新的推文
        await db()
            .update(tweets)
            .set({ isPinned: true })
            .where(and(eq(tweets.id, params.voiceId), eq(tweets.authorId, profile.id)));
    } else {
        // 取消置顶
        await db()
            .update(tweets)
            .set({ isPinned: false })
            .where(and(eq(tweets.id, params.voiceId), eq(tweets.authorId, profile.id)));
    }

    return c.json({ success: true });
});

voiceActionApi.post("/update", async (c) => {
    const params = z
        .object({
            id: z.string().min(1),
            text: z.string().min(1),
            photoUrl: z.string().nullable(),
            isHidden: z.boolean().optional(),
            warningText: z.string().nullable().optional(),
            visibility: z.enum(["public", "profileOnly", "private", "activeOnly"]),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);

    // 检查是否是自己的推文
    const [voice] = await db()
        .select()
        .from(tweets)
        .where(and(eq(tweets.id, params.id), eq(tweets.authorId, profile.id)));

    if (!voice) {
        throw new UnauthorizedError("You are not authorized to update this voice");
    }

    // 创建更新前快照
    try {
        await createSnapshot({
            tableName: 'voices',
            snapshotData: voice,
            operationType: 'UPDATE',
            userId: profile.id,
            context: c,
        });
    } catch (error) {
        console.warn('Failed to create update snapshot for voice:', error);
        // 快照失败不阻止更新操作，仅记录警告
    }

    // 检查非活跃用户是否尝试使用"仅活跃用户可见"选项
    if (params.visibility === "activeOnly" && !profile.activeUser) {
        throw new Error('仅活跃用户可以选择"仅活跃用户可见"选项');
    }

    await db()
        .update(tweets)
        .set({
            text: params.text,
            photoUrl: params.photoUrl,
            isHidden: params.isHidden ?? voice.isHidden,
            warningText: params.warningText ?? voice.warningText,
            visibility: params.visibility ?? voice.visibility,
        })
        .where(eq(tweets.id, params.id));

    return c.json({ success: true });
});

export default voiceActionApi;
