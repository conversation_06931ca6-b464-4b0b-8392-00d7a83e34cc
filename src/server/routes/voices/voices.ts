import { Hono } from "hono";
import { tweets, userFollows, userLikes, userProfiles } from "@/db/schema";
import { and, desc, eq, inArray, lt, ne, notInArray, or } from "drizzle-orm";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import { UnauthorizedError } from "../../error/UnauthorizedError";
import {
    AllResponseSchema,
    DeleteResponseSchema,
    RelatedResponseSchema,
    RepliesResponseSchema,
    UserVoiceResponseSchema,
    VoiceDetailSchema,
} from "../../schemas/voiceType";
import { keyBy } from "lodash";
import { checkVisibilityUser, queryInvisibleUserIds } from "../common";
import { createVoiceStats, getBatchVoiceStats, getProfile, getSingleVoiceStats, review, getUserRealIP } from "@/server/common";
import { db } from "@/db";
import action from "@/server/routes/voices/action";
import { generateId } from "@/lib/idUtil";
import { processMainVoice, processRetweetOf, processUserVoice } from "@/server/common/activeOnlyUtils";
import { createSnapshot } from "@/utilities/snapshot";

const voiceApi = new Hono();
const javaHost = process.env.JAVA_HOST!;

// 获取相关 voice
voiceApi.get("/related", async (c) => {
    const profile = await getProfile(c);
    // 添加游标和限制参数
    const cursor = c.req.query("cursor"); // 上一页最后一条记录的时间戳
    const limit = Number(c.req.query("limit")) || 10;

    // 合并查询关注者的tweets和用户自己的tweets
    const followingResult = await db()
        .select()
        .from(userFollows)
        .where(eq(userFollows.followerId, profile.id));

    // 查询双向黑名单（自己拉黑的和被拉黑的）
    const unviewedUserIds = await queryInvisibleUserIds(c, profile.id);

    const followingIds = followingResult.map((follow) => follow.userId);

    const voices = await db()
        .select()
        .from(tweets)
        .where(
            and(
                eq(tweets.isReply, false),
                notInArray(tweets.authorId, Array.from(unviewedUserIds)),
                or(eq(tweets.authorId, profile.id), inArray(tweets.authorId, followingIds)),
                // 添加游标条件
                cursor ? lt(tweets.createdAt, new Date(cursor)) : undefined,
                or(eq(tweets.visibility, "public"), eq(tweets.visibility, "activeOnly")),
            ),
        )
        .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
        .orderBy(desc(tweets.createdAt))
        .limit(limit + 1); // 多查一条用于判断是否有下一页

    // 判断是否有下一页
    const hasMore = voices.length > limit;
    const pagedVoices = voices.slice(0, limit);

    const retweetOriginIds = pagedVoices
        .filter((t) => t.tweets.isRetweet)
        .map((t) => t.tweets.retweetOfId!)
        .filter(Boolean);

    let retweetOrigins = [];
    if (retweetOriginIds.length > 0) {
        const relatedVoices = await db()
            .select()
            .from(tweets)
            .where(inArray(tweets.id, [...retweetOriginIds]))
            .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
            .orderBy(desc(tweets.createdAt));

        retweetOrigins.push(...relatedVoices);
    }

    // 修改map结构，使其与schema一致
    const originalForwardsMap = keyBy(
        retweetOrigins.map((o) => ({
            id: o.tweets.id,
            text: o.tweets.text,
            authorId: o.tweets.authorId,
            createdAt: o.tweets.createdAt,
            photoUrl: o.tweets.photoUrl,
            isHidden: o.tweets.isHidden || false,
            warningText: o.tweets.warningText,
            isRetweet: o.tweets.isRetweet || false,
            isReply: o.tweets.isReply || false,
            repliedToId: o.tweets.repliedToId,
            retweetOfId: o.tweets.retweetOfId,
            isPinned: o.tweets.isPinned || false,
            visibility: o.tweets.visibility || "public",
            author: o.user_profiles,
        })),
        "id",
    );

    // 获取所有voice的统计数据
    const voiceIds = [
        ...pagedVoices.map((v) => v.tweets.id),
        ...retweetOrigins.map((v) => v.tweets.id),
    ];
    const statsData = await getBatchVoiceStats(voiceIds, profile.id);
    // 组合数据
    const result = pagedVoices
        .map((r) => {
            // 如果是转发，且原作者在黑名单中，则过滤掉
            if (r.tweets.isRetweet && r.tweets.retweetOfId) {
                const originalTweet = originalForwardsMap[r.tweets.retweetOfId];
                if (originalTweet?.authorId && unviewedUserIds.has(originalTweet.authorId)) {
                    return null;
                }
            }

            const voiceId = r.tweets.id;
            // 构建统计数据
            const stats = statsData.find((i) => i.id === voiceId);

            // 处理转发内容
            let retweetOf = null;
            if (
                r.tweets.isRetweet &&
                r.tweets.retweetOfId &&
                originalForwardsMap[r.tweets.retweetOfId]
            ) {
                const originalVoiceStats = statsData.find((i) => i.id === r.tweets.retweetOfId);
                const original = originalForwardsMap[r.tweets.retweetOfId];
                retweetOf = processRetweetOf(original, originalVoiceStats, profile);
            }

            const mainVoice = processMainVoice(r.tweets, r.user_profiles, stats, profile);

            return {
                ...mainVoice,
                retweetOf,
            };
        })
        .filter(Boolean);

    // 返回时添加分页信息
    return c.json(
        RelatedResponseSchema.parse({
            success: true,
            data: result,
            hasMore,
            nextCursor: hasMore
                ? pagedVoices[pagedVoices.length - 1].tweets.createdAt.toISOString()
                : null,
        }),
    );
});

// 获取相关 voice
voiceApi.get("/all", async (c) => {
    const profile = await getProfile(c);
    // 添加游标和限制参数
    const cursor = c.req.query("cursor"); // 上一页最后一条记录的时间戳
    const limit = Number(c.req.query("limit")) || 10;

    const invisibleUserIds = await queryInvisibleUserIds(c, profile.id);
    const followedUserIds = await db()
        .select({
            userId: userFollows.userId,
        })
        .from(userFollows)
        .where(eq(userFollows.followerId, profile.id));

    const voices = await db()
        .select()
        .from(tweets)
        .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
        .where(
            and(
                eq(tweets.isReply, false),
                notInArray(tweets.authorId, Array.from(invisibleUserIds)),
                or(
                    eq(userProfiles.visibility, "public"),
                    inArray(
                        userProfiles.id,
                        followedUserIds.map((id) => id.userId),
                    ),
                ),
                cursor ? lt(tweets.createdAt, new Date(cursor)) : undefined,
                or(eq(tweets.visibility, "public"), eq(tweets.visibility, "activeOnly")),
            ),
        )
        .orderBy(desc(tweets.createdAt))
        .limit(limit + 1); // 多查一条用于判断是否有下一页

    // 判断是否有下一页
    const hasMore = voices.length > limit;
    const pagedVoices = voices.slice(0, limit);

    const retweetOriginIds = pagedVoices
        .filter((t) => t.tweets.isRetweet)
        .map((t) => t.tweets.retweetOfId!)
        .filter(Boolean);

    let retweetOrigins = [];
    if (retweetOriginIds.length > 0) {
        const relatedVoices = await db()
            .select()
            .from(tweets)
            .where(inArray(tweets.id, [...retweetOriginIds]))
            .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
            .orderBy(desc(tweets.createdAt));

        retweetOrigins.push(...relatedVoices);
    }

    // 修改map结构，使其与schema一致
    const originalForwardsMap = keyBy(
        retweetOrigins.map((o) => ({
            id: o.tweets.id,
            text: o.tweets.text,
            authorId: o.tweets.authorId,
            createdAt: o.tweets.createdAt,
            photoUrl: o.tweets.photoUrl,
            isHidden: o.tweets.isHidden || false,
            warningText: o.tweets.warningText,
            isRetweet: o.tweets.isRetweet || false,
            isReply: o.tweets.isReply || false,
            repliedToId: o.tweets.repliedToId,
            retweetOfId: o.tweets.retweetOfId,
            isPinned: o.tweets.isPinned || false,
            visibility: o.tweets.visibility || "public",
            author: o.user_profiles,
        })),
        "id",
    );

    // 获取所有voice的统计数据
    const voiceIds = [
        ...pagedVoices.map((v) => v.tweets.id),
        ...retweetOrigins.map((v) => v.tweets.id),
    ];
    const statsData = await getBatchVoiceStats(voiceIds, profile.id);

    // 组合数据
    const result = pagedVoices
        .map((r) => {
            // 如果是转发，且原作者在黑名单中，则过滤掉
            if (r.tweets.isRetweet && r.tweets.retweetOfId) {
                const originalTweet = originalForwardsMap[r.tweets.retweetOfId];
                if (originalTweet?.authorId && invisibleUserIds.has(originalTweet.authorId)) {
                    return null;
                }
            }

            const voiceId = r.tweets.id;
            // 构建统计数据
            const stats = createVoiceStats(voiceId, statsData);

            // 处理转发内容
            let retweetOf = null;
            if (
                r.tweets.isRetweet &&
                r.tweets.retweetOfId &&
                originalForwardsMap[r.tweets.retweetOfId]
            ) {
                const originalVoiceStats = createVoiceStats(r.tweets.retweetOfId, statsData);
                const original = originalForwardsMap[r.tweets.retweetOfId];
                retweetOf = processRetweetOf(original, originalVoiceStats, profile);
            }

            const mainVoice = processMainVoice(r.tweets, r.user_profiles, stats, profile);

            return {
                ...mainVoice,
                retweetOf,
            };
        })
        .filter(Boolean);

    // 返回时添加分页信息
    return c.json(
        AllResponseSchema.parse({
            success: true,
            data: result,
            hasMore,
            nextCursor: hasMore
                ? pagedVoices[pagedVoices.length - 1].tweets.createdAt.toISOString()
                : null,
        }),
    );
});

voiceApi.post("/delete/:id", async (c) => {
    const params = z
        .object({
            id: z.string().min(1),
        })
        .parse({
            id: c.req.param("id"),
        });

    const profile = await getProfile(c);
    const tweet = await db()
        .select()
        .from(tweets)
        .where(and(eq(tweets.id, params.id), eq(tweets.authorId, profile.id)))
        .limit(1);

    if (!tweet.length) {
        throw new Error("无权限删除");
    }

    // 创建删除前快照
    try {
        await createSnapshot({
            tableName: 'voices',
            snapshotData: tweet[0],
            operationType: 'DELETE',
            userId: profile.id,
            context: c,
        });
    } catch (error) {
        console.warn('Failed to create delete snapshot for voice:', error);
        // 快照失败不阻止删除操作，仅记录警告
    }

    await Promise.all([
        db().delete(userLikes).where(eq(userLikes.voiceId, params.id)),
        db()
            .update(tweets)
            .set({ repliedToId: null })
            .where(and(eq(tweets.repliedToId, params.id), eq(tweets.isReply, true))),
        db().delete(tweets).where(eq(tweets.id, params.id)),
    ]);

    return c.json(
        DeleteResponseSchema.parse({
            success: true,
            message: "成功删除",
        }),
    );
});

voiceApi.get("/reply/:id", async (c) => {
    const params = z
        .object({
            id: z.string().min(1),
        })
        .parse({
            id: c.req.param("id"),
        });

    const profile = await getProfile(c);
    const invisibleUserIds = await queryInvisibleUserIds(c, profile.id);

    const replies = await db()
        .select()
        .from(tweets)
        .where(
            and(
                eq(tweets.repliedToId, params.id),
                notInArray(tweets.authorId, Array.from(invisibleUserIds)),
            ),
        )
        .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
        .orderBy(desc(tweets.createdAt));

    const replyIds = replies.map((reply) => reply.tweets.id);

    const statsData = await getBatchVoiceStats(replyIds, profile.id, invisibleUserIds);

    return c.json(
        RepliesResponseSchema.parse({
            success: true,
            data: replies.map((r) => ({
                ...r.tweets,
                stats: statsData.find((s: any) => s.id === r.tweets.id),
                author: r.user_profiles,
            })),
        }),
    );
});

voiceApi.get("/user/:id", async (c) => {
    const params = z
        .object({
            id: z.string().min(1),
        })
        .parse({
            id: c.req.param("id"),
        });

    const cursor = c.req.query("cursor");
    const limit = Number(c.req.query("limit")) || 10;

    const profile = await getProfile(c);
    const unviewedUserIds = await queryInvisibleUserIds(c, profile.id);
    const isVisibilityUser = await checkVisibilityUser(profile.id, params.id);

    if (unviewedUserIds.has(params.id) || !isVisibilityUser) {
        return c.json(
            UserVoiceResponseSchema.parse({
                success: true,
                data: [],
                hasMore: false,
                nextCursor: null,
                total: 0,
            }),
        );
    }

    const voices = await db()
        .select()
        .from(tweets)
        .where(
            and(
                eq(tweets.authorId, params.id),
                notInArray(tweets.authorId, Array.from(unviewedUserIds)),
                eq(tweets.isReply, false),
                cursor ? lt(tweets.createdAt, new Date(cursor)) : undefined,
                params.id !== profile.id ?
                    and(
                        ne(tweets.visibility, "private"),
                        or(eq(tweets.visibility, "public"), eq(tweets.visibility, "activeOnly")),
                    ) :
                    undefined,
            ),
        )
        .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
        .orderBy(desc(tweets.createdAt))
        .limit(limit + 1);

    if (voices.length === 0) {
        return c.json(
            UserVoiceResponseSchema.parse({
                success: true,
                data: [],
                hasMore: false,
                nextCursor: null,
                total: 0,
            }),
        );
    }

    const hasMore = voices.length > limit;
    const pagedVoices = voices.slice(0, limit);

    const retweetOrigins = pagedVoices
        .map((voice) => (voice.tweets.isRetweet ? voice.tweets.retweetOfId : null))
        .filter((id): id is string => id !== null);

    let map: Record<string, any> = {};
    if (retweetOrigins.length > 0) {
        const originalForwards = await db()
            .select()
            .from(tweets)
            .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
            .where(inArray(tweets.id, retweetOrigins));

        map = keyBy(
            originalForwards.map((o) => ({
                id: o.tweets.id,
                text: o.tweets.text,
                authorId: o.tweets.authorId,
                createdAt: o.tweets.createdAt,
                photoUrl: o.tweets.photoUrl,
                isHidden: o.tweets.isHidden || false,
                warningText: o.tweets.warningText,
                isRetweet: o.tweets.isRetweet || false,
                isReply: o.tweets.isReply || false,
                repliedToId: o.tweets.repliedToId,
                retweetOfId: o.tweets.retweetOfId,
                isPinned: o.tweets.isPinned || false,
                visibility: o.tweets.visibility || "public",
                author: o.user_profiles,
            })),
            "id",
        );
    }

    // 获取所有voice的统计数据
    const voiceIds = [...pagedVoices.map((v) => v.tweets.id), ...retweetOrigins];
    const statsData = await getBatchVoiceStats(voiceIds, profile.id);

    // 组合数据
    const result = pagedVoices
        .map((r) => {
            // 如果是转发，且原作者在黑名单中，则过滤掉
            if (r.tweets.isRetweet && r.tweets.retweetOfId) {
                const originalTweet = map[r.tweets.retweetOfId];
                if (originalTweet?.authorId && unviewedUserIds.has(originalTweet.authorId)) {
                    return null;
                }
            }

            const voiceId = r.tweets.id;
            // 构建统计数据
            const stats = createVoiceStats(voiceId, statsData);

            // 处理转发内容
            let retweetOf = null;
            if (r.tweets.isRetweet && r.tweets.retweetOfId && map[r.tweets.retweetOfId]) {
                const originalVoiceStats = createVoiceStats(r.tweets.retweetOfId, statsData);
                const original = map[r.tweets.retweetOfId];
                retweetOf = processRetweetOf(original, originalVoiceStats, profile);
            }

            const mainVoice = processUserVoice(r.tweets, r.user_profiles, stats, profile);

            return {
                ...mainVoice,
                retweetOf,
            };
        })
        .filter(Boolean);

    // 返回时添加分页信息
    return c.json(
        UserVoiceResponseSchema.parse({
            success: true,
            data: result,
            hasMore,
            nextCursor: hasMore
                ? pagedVoices[pagedVoices.length - 1].tweets.createdAt.toISOString()
                : null,
        }),
    );
});

voiceApi.get("/detail/:id", async (c) => {
    const params = z
        .object({
            id: z.string().min(1),
        })
        .parse({
            id: c.req.param("id"),
        });

    const profile = await getProfile(c);
    const invisibleUserIds = await queryInvisibleUserIds(c, profile.id);

    const [voice] = await db()
        .select()
        .from(tweets)
        .where(eq(tweets.id, params.id))
        .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
        .limit(1);

    if (!voice) {
        throw new HTTPException(404, { message: "Voice not found" });
    }

    if (voice.tweets.visibility === "private" && profile.id !== voice.tweets.authorId) {
        throw new HTTPException(404, { message: "access define" });
    }

    if (voice.tweets.visibility === "activeOnly" && profile.activeUser === false && profile.id !== voice.tweets.authorId) {
        throw new HTTPException(404, { message: "仅活跃用户可见" });
    }

    if (invisibleUserIds.has(voice.tweets.authorId)) {
        throw new UnauthorizedError("You are not authorized to view this voice");
    }

    const [originalVoice] = voice.tweets.retweetOfId
        ? await db()
            .select()
            .from(tweets)
            .leftJoin(userProfiles, eq(userProfiles.id, tweets.authorId))
            .where(eq(tweets.id, voice.tweets.retweetOfId))
            .limit(1)
        : [];

    // 获取当前voice的统计数据
    const stats = await getSingleVoiceStats(voice.tweets.id, profile?.id);

    // 如果有原始被转发的推文，获取其统计数据
    let originalVoiceStats = null;
    if (originalVoice) {
        originalVoiceStats = await getSingleVoiceStats(originalVoice.tweets.id, profile?.id);
    }

    // 准备repliedTo字段，确保它是正确的格式
    let repliedTo = null;
    if (voice.tweets.repliedToId) {
        // 如果有repliedToId，创建一个对象
        repliedTo = {
            id: voice.tweets.repliedToId,
        };
    }

    const result = processMainVoice(
        voice.tweets,
        voice.user_profiles,
        stats,
        profile,
    );

    // 添加转发和回复信息
    result.retweetOf = originalVoice
        ? processRetweetOf(
            {
                id: originalVoice.tweets.id,
                text: originalVoice.tweets.text,
                authorId: originalVoice.tweets.authorId,
                createdAt: originalVoice.tweets.createdAt,
                photoUrl: originalVoice.tweets.photoUrl,
                isHidden: originalVoice.tweets.isHidden,
                warningText: originalVoice.tweets.warningText,
                isRetweet: originalVoice.tweets.isRetweet,
                isReply: originalVoice.tweets.isReply,
                repliedToId: originalVoice.tweets.repliedToId,
                retweetOfId: originalVoice.tweets.retweetOfId,
                isPinned: originalVoice.tweets.isPinned,
                visibility: originalVoice.tweets.visibility,
                author: originalVoice.user_profiles,
            },
            originalVoiceStats,
            profile,
        )
        : null;
    result.repliedTo = repliedTo;

    return c.json({
        success: true,
        data: VoiceDetailSchema.parse(result),
    });
});

voiceApi.post("/create", async (c) => {
    const params = z
        .object({
            text: z.string().min(1).max(999),
            photoUrl: z.string().nullable().optional(),
            isHidden: z.boolean().optional(),
            warningText: z.string().nullable().optional(),
            visibility: z.enum(["public", "profileOnly", "private", "activeOnly"]),
        })
        .parse(await c.req.json());

    const profile = await getProfile(c);

    if (profile.id === "") {
        throw new Error("禁言中");
    }

    // 检查非活跃用户是否尝试使用"仅活跃用户可见"选项
    if (params.visibility === "activeOnly" && !profile.activeUser) {
        throw new Error("仅活跃用户可以选择\"仅活跃用户可见\"选项");
    }

    const [voice] = await db()
        .insert(tweets)
        .values({
            id: generateId(),
            text: params.text,
            photoUrl: params.photoUrl,
            authorId: profile.id,
            isHidden: params.isHidden ?? false,
            warningText: params.warningText ?? null,
            createdAt: new Date(),
            visibility: params.visibility,
        })
        .returning();

    // 异步执行
    review(c, voice.id, voice.text, profile.userId, profile.username);

    return c.json({
        success: true,
        data: voice,
    });
});

voiceApi.post("/recommend", async (c) => {
    const refreshParam = c.req.query("refresh");
    const refresh = refreshParam === "true";
    const profile = await getProfile(c);
    const userIP = getUserRealIP(c);
    const response = await fetch(`${javaHost}/api/recommendations/voices`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-Real-IP": userIP,
            "X-Forwarded-For": userIP,
        },
        body: JSON.stringify({
            userId: profile.id,
            refresh: refresh,
            size: 10,
        }),
    });

    if (!response.ok) {
        throw new Error("Failed to fetch recommendations");
    }
    const pagedVoices = await response.json();

    // 获取所有voice的统计数据
    const voiceIds: string[] = [
        ...pagedVoices.map((v: any) => v.id),
    ];
    const profileIds: string[] = pagedVoices.map((v: any) => v.authorId);
    const profiles = await db().select().from(userProfiles).where(inArray(userProfiles.id, profileIds));
    const statsData = await getBatchVoiceStats(voiceIds, profile.id);

    // 组合数据
    const result = pagedVoices
        .map((r: any) => {
            const voiceId = r.id;
            // 构建统计数据
            const stats = statsData.find((i) => i.id === voiceId);
            const author = profiles.find((p) => p.id === r.authorId);

            const mainVoice = processMainVoice(r, author, stats, profile);

            return {
                ...mainVoice,
                author,
                stats,
                retweetOf: null,
                repliedTo: null,
            };
        })
        .filter(Boolean);

    // 返回时添加分页信息
    return c.json(
        RelatedResponseSchema.parse({
            success: true,
            data: result,
            hasMore: true,
            nextCursor: null,
        }),
    );
});

voiceApi.route("/action", action);
export default voiceApi;
