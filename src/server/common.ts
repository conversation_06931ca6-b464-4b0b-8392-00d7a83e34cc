import { Context, Hono } from "hono";
import { and, count, eq, inArray, notInArray, or } from "drizzle-orm";
import { UnauthorizedError } from "./error/UnauthorizedError";
import { db } from "@/db";
import { tweets, userLikes, userProfiles } from "@/db/schema";
import { AiReviewResult } from "@/server/schemas/aiReview";
import { createOpenAI } from "@ai-sdk/openai";
import { REVIEW_PROMPT } from "@/server/config/reviewPrompts";
import { generateText, Output } from "ai";
import { env } from "hono/adapter";
import { VoiceStat } from "@/server/schemas/voiceType";

export const PAGE_SIZE = 10;

export const getProfile = async (c: Context) => {
    const tokenId = getAuth(c);

    const [profile] = await db()
        .select()
        .from(userProfiles)
        .where(or(eq(userProfiles.userId, tokenId), eq(userProfiles.id, tokenId)));

    if (!profile) {
        // 补偿机制，避免创建用户失败
        return await createDefaultUser(tokenId);
    }

    return profile;
};

export const getAuth = (c: Context) => {
    const payload = c.get("jwtPayload");
    const tokenId = payload.user?.id || payload.session?.user?.id;
    if (!tokenId) {
        throw new UnauthorizedError("Not authorized");
    }
    return tokenId;
};

export const review = async (c: Context, voiceId: string, text: string, userId: string, name: string) => {
    const openai = createOpenAI({
        apiKey: env<{ OPENAI_API_KEY: string }>(c).OPENAI_API_KEY,
        baseURL: env<{ OPENAI_BASE_URL: string }>(c).OPENAI_BASE_URL,
    });

    const reviewContent = REVIEW_PROMPT + `<content>${text}</content>`;
    let level = "NORMAL";
    const result = await generateText({
        model: openai("gpt-4.1-nano"),
        messages: [
            {
                role: "system",
                content: reviewContent,
            },
        ],
        experimental_output: Output.object({ schema: AiReviewResult }),
    });
    const firstResult = result.experimental_output;
    level = firstResult.level;
    if (firstResult.level !== "NORMAL" && firstResult.level !== "QUESTION") {
        const second = await generateText({
            model: openai("gpt-4.1"),
            messages: [
                {
                    role: "system",
                    content: reviewContent,
                },
            ],
            experimental_output: Output.object({ schema: AiReviewResult }),
        });
        const secondResult = second.experimental_output;
        level = secondResult.level;
        if (secondResult.level !== "NORMAL" && secondResult.level !== "QUESTION") {
            // 发送通知到飞书
            const feishuWebhookUrl = env<{ FEISHU_WEBHOOK_URL: string }>(c).FEISHU_WEBHOOK_URL;
            if (feishuWebhookUrl) {
                fetch(feishuWebhookUrl, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        "msg_type": "text",
                        "content": {
                            "text": text,
                            "name": name,
                            "userId": userId,
                            "reason": secondResult.reason
                        }
                    })
                }).catch(error => {
                    console.error("Failed to send notification to Feishu:", error);
                });
            }
        }
    }
    const javaHost = env<{ JAVA_HOST: string }>(c).JAVA_HOST;
    if (javaHost) {
        const userIP = getUserRealIP(c);
        fetch(`${javaHost}/api/voice/stat/ai-score`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-Real-IP": userIP,
                "X-Forwarded-For": userIP,
            },
            body: JSON.stringify({
                voiceId: voiceId,
                aiScore: level,
            }),
        }).catch(error => {
            console.error("Failed to send ai score to java:", error);
        });
    }
};

// 获取单个声音的统计数据
export const getSingleVoiceStats = async (voiceId: string, profileId?: string) => {
    const [likesCount, retweetsCount, repliesCount, isLikedByMe = false, isRetweetedByMe = false] =
        await Promise.all([
            db()
                .select({ count: count() })
                .from(userLikes)
                .where(eq(userLikes.voiceId, voiceId))
                .then((res) => res[0]?.count || 0),
            db()
                .select({ count: count() })
                .from(tweets)
                .where(eq(tweets.retweetOfId, voiceId))
                .then((res) => res[0]?.count || 0),
            db()
                .select({ count: count() })
                .from(tweets)
                .where(and(eq(tweets.repliedToId, voiceId), eq(tweets.isReply, true)))
                .then((res) => res[0]?.count || 0),
            profileId
                ? db()
                    .select()
                    .from(userLikes)
                    .where(and(eq(userLikes.voiceId, voiceId), eq(userLikes.userId, profileId)))
                    .then((res) => res.length > 0)
                : Promise.resolve(false),
            profileId
                ? db()
                    .select()
                    .from(tweets)
                    .where(
                        and(eq(tweets.retweetOfId, voiceId), eq(tweets.authorId, profileId)),
                    )
                    .then((res) => res.length > 0)
                : Promise.resolve(false),
        ]);

    return {
        id: voiceId,
        likeCount: likesCount as number,
        retweetCount: retweetsCount as number,
        replyCount: repliesCount as number,
        isLikedByMe,
        isRetweetedByMe,
    };
};

export const getBatchVoiceStats = async (
    voiceIds: string[],
    profileId?: string,
    invisibleUserId?: Set<string>,
) => {
    // 同一条SQL语句中获取多条声音的统计信息
    const [likesCounts, forwardCounts, repliesCounts, likeStats, forwardStats] = await Promise.all([
        db()
            .select({
                voiceId: userLikes.voiceId,
                count: count(),
            })
            .from(userLikes)
            .where(inArray(userLikes.voiceId, voiceIds))
            .groupBy(userLikes.voiceId),
        db()
            .select({
                voiceId: tweets.retweetOfId,
                count: count(),
            })
            .from(tweets)
            .where(inArray(tweets.retweetOfId, voiceIds))
            .groupBy(tweets.retweetOfId),
        db()
            .select({
                voiceId: tweets.repliedToId,
                count: count(),
            })
            .from(tweets)
            .where(
                and(
                    inArray(tweets.repliedToId, voiceIds),
                    invisibleUserId
                        ? notInArray(tweets.authorId, Array.from(invisibleUserId))
                        : undefined,
                    eq(tweets.isReply, true),
                ),
            )
            .groupBy(tweets.repliedToId),
        profileId
            ? db()
                .select({
                    voiceId: userLikes.voiceId,
                })
                .from(userLikes)
                .where(and(inArray(userLikes.voiceId, voiceIds), eq(userLikes.userId, profileId)))
            : Promise.resolve([]),
        profileId
            ? db()
                .select({
                    voiceId: tweets.retweetOfId,
                })
                .from(tweets)
                .where(
                    and(
                        inArray(tweets.retweetOfId, voiceIds),
                        eq(tweets.authorId, profileId),
                    ),
                )
            : Promise.resolve([]),
    ]);

    return voiceIds.map((voiceId) => ({
        id: voiceId,
        likeCount: likesCounts.find((s) => s.voiceId === voiceId)?.count || 0,
        retweetCount: forwardCounts.find((s) => s.voiceId === voiceId)?.count || 0,
        replyCount: repliesCounts.find((s) => s.voiceId === voiceId)?.count || 0,
        isLikedByMe: likeStats.some((like) => like.voiceId === voiceId),
        isRetweetedByMe: forwardStats.some((retweet) => retweet.voiceId === voiceId),
    }));
};

export const createVoiceStats = (voiceId: string, statsData: VoiceStat[]): VoiceStat => {
    return statsData.find((stats) => stats.id === voiceId) || {
        id: voiceId,
        likeCount: 0,
        retweetCount: 0,
        replyCount: 0,
        isLikedByMe: false,
        isRetweetedByMe: false,
    };
};

export const createDefaultUser = async (userId: string) => {
    const [profile] = await db()
        .insert(userProfiles)
        .values({
            id: userId,
            userId: userId,
            username: `Qöo_${Math.floor(Math.random() * 100)}${userId.slice(0, 3)}`,
            photoUrl: "profile/default-avatar.png",
            gender: "preferNotToSay",
            headerUrl: "profile/default-header.png",
        })
        .returning();
    return profile;
};

/**
 * 获取用户的真实 IP 地址
 * @param c Hono Context
 * @returns 用户的真实IP地址
 */
export const getUserRealIP = (c: Context): string => {
    // 优先从常用的代理头部获取真实 IP
    const xForwardedFor = c.req.header('X-Forwarded-For');
    const xRealIP = c.req.header('X-Real-IP');
    const cfConnectingIP = c.req.header('CF-Connecting-IP');
    
    if (xForwardedFor) {
        // X-Forwarded-For 可能包含多个 IP，取第一个
        return xForwardedFor.split(',')[0].trim();
    }
    
    if (xRealIP) {
        return xRealIP;
    }
    
    if (cfConnectingIP) {
        return cfConnectingIP;
    }
    
    // 如果都没有，返回未知
    return 'unknown';
};
