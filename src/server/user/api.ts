import { Context, Hono } from "hono";
import { createDefaultUser, getAuth, getProfile, getUserRealIP } from "../common";
import { queryInvisibleUser, queryInvisibleUserIds } from "../routes/common";
import { blockedUsers, tweets, userFollows, userProfiles } from "@/db/schema";
import { eq, and, ilike, notInArray, or, count } from "drizzle-orm";
import {
    CreateProfileResponseSchema,
    CreateProfileSchema,
    ProfileResponseSchema,
    RandomResponseSchema,
    UpdateProfileResponseSchema,
    UpdateProfileSchema,
} from "../schemas/user";
import { z } from "zod";
import { db } from "@/db";
import { profileResponseSchema, updateVisibilityRequest } from "@/server/user/schema";

export const userApi = new Hono();

const javaHost = process.env.JAVA_HOST!;

userApi.get("/random", async (c) => {
    const profile = await getProfile(c);
    const [unviewedUserIds, followingIds] = await Promise.all([
        queryInvisibleUserIds(c, profile.id),
        db().select().from(userFollows).where(eq(userFollows.followerId, profile.id)),
    ]);

    const randomUsers = await db()
        .select()
        .from(userProfiles)
        .where(
            and(
                notInArray(userProfiles.id, Array.from(unviewedUserIds)),
                notInArray(userProfiles.id, Array.from(followingIds.map((f) => f.userId)))
            )
        )
        .limit(20);

    // 随机打乱数组并取前三个用户
    const shuffledUsers = [...randomUsers].sort(() => Math.random() - 0.5).slice(0, 3);

    return c.json(
        RandomResponseSchema.parse({
            success: true,
            data: shuffledUsers,
        })
    );
});

userApi.get("/profile/:userId", async (c) => {
    const profile = await getProfile(c);
    const userId = z.string().parse(c.req.param("userId"));
    const unviewedUser = await db()
        .select()
        .from(blockedUsers)
        .where(
            or(
                and(eq(blockedUsers.userId, profile.id), eq(blockedUsers.blockedUserId, userId)),
                and(eq(blockedUsers.blockedUserId, profile.id), eq(blockedUsers.userId, userId))
            )
        );

    // 如果用户拉黑了自己直接404
    if (unviewedUser.find((u) => u.userId === userId)) {
        return c.json({ success: false, message: "用户不存在", errorCode: "U0000" }, 200);
    }

    // 自己拉黑了用户
    if (unviewedUser.find((u) => u.blockedUserId === userId)) {
        return c.json({ success: false, message: "拉黑了用户", errorCode: "U0001" }, 200);
    }
    // 并行查询用户信息和关注数据
    const [[userProfile], followers, following, [totalVoices]] = await Promise.all([
        db().select().from(userProfiles).where(eq(userProfiles.id, userId)).limit(1),
        db()
            .select()
            .from(userFollows)
            .leftJoin(userProfiles, eq(userProfiles.id, userFollows.followerId))
            .where(eq(userFollows.userId, userId)),
        db()
            .select()
            .from(userFollows)
            .leftJoin(userProfiles, eq(userProfiles.id, userFollows.userId))
            .where(eq(userFollows.followerId, userId)),

        db()
            .select({ count: count() })
            .from(tweets)
            .where(and(eq(tweets.authorId, userId), eq(tweets.isReply, false)))
            .limit(1),
    ]);

    if (!userProfile) {
        return c.json({ success: true, data: null }, 200);
    }

    const followerResp = followers.map((f) => f.user_profiles);

    return c.json(
        ProfileResponseSchema.parse({
            success: true,
            data: {
                ...userProfile,
                isFollowedByMe: !!followers.find((f) => f.user_profiles?.id === profile.id),
                followers: followerResp,
                following: following.map((f) => f.user_profiles),
                totalVoices: totalVoices.count,
                visibility:
                    profile.id === userId || // 用户查看自己的主页时总是可见
                    userProfile.visibility === "public" ||
                    followerResp.some((user) => user && user.id === profile.id),
            },
        })
    );
});

userApi.post("/update", async (c) => {
    const profile = await getProfile(c);
    const updateProfile = UpdateProfileSchema.parse(await c.req.json());

    const [result] = await db()
        .update(userProfiles)
        .set({
            ...updateProfile,
        })
        .where(eq(userProfiles.id, profile.id))
        .returning();

    return c.json(
        profileResponseSchema.parse({
            success: true,
            data: result,
        })
    );
});

userApi.get("/search", async (c) => {
    const profile = await getProfile(c);
    const search = z.string().parse(c.req.query("q"));

    const unviewedUserIds = await queryInvisibleUserIds(c, profile.id);

    const users = await db()
        .select()
        .from(userProfiles)
        .where(
            and(
                ilike(userProfiles.username, `%${search}%`),
                notInArray(userProfiles.id, Array.from(unviewedUserIds))
            )
        )
        .limit(5);

    return c.json(
        RandomResponseSchema.parse({
            success: true,
            data: users,
        })
    );
});

userApi.post("/create", async (c) => {
    const createProfile = CreateProfileSchema.parse(await c.req.json());
    const [exist] = await db()
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.userId, createProfile.userId))
        .limit(1);
    if (exist) {
        return c.json({ success: false, message: "用户已存在", errorCode: "U0002" }, 200);
    }
    const [profile] = await db()
        .insert(userProfiles)
        .values({
            id: createProfile.userId,
            ...createProfile,
        })
        .returning();

    return c.json(
        CreateProfileResponseSchema.parse({
            success: true,
            data: profile,
        })
    );
});

userApi.get("/username/:userId", async (c) => {
    const userId = c.req.param("userId");
    const [profile] = await db()
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.id, userId))
        .limit(1);
    return c.json({ success: true, data: profile.username });
});

userApi.get("/sign-in-profile", async (c) => {
    const userId: string = await getAuth(c);
    const userIP = getUserRealIP(c);
    
    const response = await fetch(`${javaHost}/api/user`, {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            // 将用户的真实 IP 传递给 Java 服务
            "X-Real-IP": userIP,
            "X-Forwarded-For": userIP,
        },
        body: new URLSearchParams({ userId }),
    });
    if (!response.ok) {
        throw new Error("Failed to fetch user");
    }
    const profile = await getProfile(c);
    return c.json({
        success: true,
        data: profile
    });
});

userApi.post("/visibility", async (c) => {
    const { option } = updateVisibilityRequest.parse(await c.req.json());
    const profile = await getProfile(c);
    const [newProfile] = await db()
        .update(userProfiles)
        .set({
            visibility: option,
        })
        .where(eq(userProfiles.id, profile.id))
        .returning();
    return c.json(
        profileResponseSchema.parse({
            success: true,
            data: newProfile,
        })
    );
});

userApi.post("/create-default", async (c) => {
    const auth: string = await getAuth(c);

    const [exist] = await db()
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.userId, auth))
        .limit(1);

    if (exist) {
        return c.json({ success: true }, 200);
    }

    // 创建具有默认值的用户资料
    const profile = await createDefaultUser(auth);

    return c.json(
        CreateProfileResponseSchema.parse({
            success: true,
            data: profile,
        })
    );
});

export default userApi;
