import service from "@/service";
import { Context } from "hono";
import { getUserRealIP } from "@/server/common";

// 快照类型定义
export interface SnapshotRequest {
  tableName: string;
  snapshotData: Record<string, any>;
  operationType: 'UPDATE' | 'DELETE';
  userId?: string;
  context?: Context; // 添加可选的Context参数用于获取用户IP
}

/**
 * 序列化数据，处理BigInt类型
 * 只有数据库内部的 id 字段是 BigInt，业务 ID 都是 text 类型
 * 为了保持查询一致性，我们排除内部 id 字段，只保存业务相关字段
 */
function serializeData(obj: any): any {
  // 复制对象并排除数据库内部的 bigint id 字段
  const { id, ...businessData } = obj;
  
  return JSON.parse(JSON.stringify(businessData, (key, value) =>
    typeof value === 'bigint' ? value.toString() : value
  ));
}

/**
 * 创建数据快照
 */
export async function createSnapshot(data: SnapshotRequest): Promise<void> {
  const javaHost = process.env.JAVA_HOST;
  
  if (!javaHost) {
    throw new Error('JAVA_HOST environment variable is not configured');
  }

  // 处理BigInt序列化问题，排除内部数据库ID
  const serializedData = {
    ...data,
    snapshotData: serializeData(data.snapshotData)
  };

  // 准备请求头
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };

  // 如果提供了context，添加用户真实IP到请求头
  if (data.context) {
    const userIP = getUserRealIP(data.context);
    headers['X-Real-IP'] = userIP;
    headers['X-Forwarded-For'] = userIP;
  }

  await service.post(`${javaHost}/api/snapshots`, serializedData, {
    headers
  });
}

 