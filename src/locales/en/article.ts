export const article = {
    // 按钮和操作
    publish: "Publish Article",
    update: "Update Article",
    edit: "Edit",
    delete: "Delete",
    like: "Like",
    unlike: "Unlike",
    previewInNewWindow: "Preview in New Window",
    previewMode: "Article Preview Mode",
    previewModeDesc: "This is not the final published version",
    previewHint: "This is an article preview, return to the editor to continue editing or publish the article",

    // 表单字段
    title: "Title",
    content: "Content",
    summary: "Summary",
    coverImage: "Cover Image",
    tags: "Tags",
    warningText: "Warning Text",

    // 占位符
    titlePlaceholder: "Enter title",
    summaryPlaceholder: "Enter summary (optional)",
    contentPlaceholder: "Start writing...",
    tagsPlaceholder: "Enter tags and press Enter to add (max 5)",
    warningPlaceholder: "Enter warning message",

    // 设置
    settings: "Article Settings",
    visibility: "Visibility",
    visibilityPublic: "Public",
    visibilityLoggedIn: "Logged-in users only",
    visibilityPublicDesc: "Visible to everyone",
    visibilityLoggedInDesc: "Visible to logged-in users only",
    contentWarning: "Add content warning",
    visibilityQuestion: "Who can see this article?",

    // 内容类型
    contentType: "Content Type",
    contentTypeQuestion: "Please select content type",
    contentTypeOriginal: "Original First Release",
    contentTypeDistribution: "Content Distribution",
    contentTypeTranslation: "Translation Content",
    contentTypeOriginalDesc: "Original content first published on platform",
    contentTypeDistributionDesc: "Content distribution from other platforms",
    contentTypeTranslationDesc: "Content translated from other languages",

    // 翻译授权
    translationAuth: "Translation Authorization",
    translationAuthRequired: "Translation content requires authorization details",
    translationAuthPlaceholder: "Please explain translation authorization source, e.g.: authorized by original author, fair use, public domain, etc.",
    originalAuthor: "Original Author",
    originalAuthorPlaceholder: "Please enter original author name",
    originalSource: "Original Source",
    originalSourcePlaceholder: "Please enter original text link or source",
    translationPermission: "Authorization Details",
    translationPermissionPlaceholder: "Please provide detailed translation authorization information",

    // 创作激励选择
    incentiveChoice: "Creative Incentive",
    applyForIncentive: "Apply for Creative Incentive",
    noIncentiveMode: "Free Creation Mode",
    incentiveChoiceQuestion: "Do you want to apply for creative incentives?",
    applyForIncentiveDesc: "Enjoy creative incentives, follow corresponding agreement terms",
    noIncentiveModeDesc: "Waive creative incentives, enjoy more relaxed agreement terms",
    incentiveAgreementNote: "Choosing to apply for incentives will apply incentive agreement terms, including deletion restrictions",
    freeAgreementNote: "Choosing free mode will not enjoy incentives, but the agreement is more relaxed",

    // 编辑限制提示
    incentiveContentTypeRestriction: "Articles that applied for creative incentive cannot modify content type",
    incentiveChoiceRestriction: "Articles that applied for creative incentive cannot modify incentive application status",
    incentiveTranslationRestriction: "Articles that applied for creative incentive cannot modify translation authorization information",

    // 状态和消息
    submitting: "Submitting...",
    publishSuccess: "Article published successfully",
    updateSuccess: "Article updated successfully",
    publishSuccessDesc: "Your article has been published",
    updateSuccessDesc: "Your article has been updated",
    operationFailed: "Operation failed",
    retryLater: "Please try again later",
    coverImageUploadFailed: "Cover image upload failed",

    // 验证消息
    titleMinLength: "Title must be at least 5 characters",
    titleMaxLength: "Title must be at most 30 characters",
    titleRequired: "Title is required",
    contentMinLength: "Content must be at least 300 characters",
    contentRequired: "Content is required",
    summaryMaxLength: "Summary must be at most 200 characters",
    warningMaxLength: "Warning text must be at most 50 characters",
    warningRequired: "Warning text is required",
    tagMaxLength: "Tag must be at most 20 characters",
    tagMaxCount: "Maximum 5 tags allowed",
    tagLimitReached: "Tag limit reached",

    // 其他
    coverImageUpload: "Click to upload cover image",
    coverImagePreview: "Cover preview",
    showContent: "Show content",
    contentWarningTitle: "Content warning",
    contentWarningDefault: "This content may contain sensitive information",
    onlyLoggedInVisible: "Visible to logged-in users only",

    // 删除确认
    deleteConfirm: "Confirm deletion",
    deleteConfirmDesc: "This action cannot be undone. Are you sure you want to delete this article?",
    deleteSuccess: "Deleted successfully",
    deleteSuccessDesc: "Article has been successfully deleted",
    deleteFailed: "Deletion failed",
    deleteIncentiveRestriction: "Cannot Delete",
    deleteIncentiveRestrictionDesc: "Articles that applied for creative incentive cannot be deleted within 90 days of publication to ensure fairness of the incentive mechanism.",

    // 权限
    noEditPermission: "No edit permission",
    noEditPermissionDesc: "You can only edit your own articles",
    loginRequired: "Please log in first",
    loginRequiredDesc: "You need to log in to like articles",

    // 创作者协议
    creatorAgreement: "Creator Agreement",
    agreementTitle: "QueerEcho Content Creation Agreement",
    agreeToTerms: "I have read and agree to the Creator Agreement",
    agreeToTermsPrefix: "I have read and agree to",
    mustAgreeToTerms: "You must agree to the Creator Agreement before publishing",

    // 协议类型
    originalAgreementTitle: "QueerEcho Original First Release Agreement",
    distributionAgreementTitle: "QueerEcho Content Distribution Agreement",
    translationAgreementTitle: "QueerEcho Translation Content Agreement",
    incentiveAgreementTitle: "QueerEcho Creative Incentive Agreement",
    freeAgreementTitle: "QueerEcho Free Creation Agreement",
    viewFullAgreement: "View Full Agreement",
    closeAgreement: "Close Agreement",

    // 翻译信息显示
    translationInfo: "Translation Authorization",
    originalAuthorLabel: "Original Author:",
    originalSourceLabel: "Original Source:",
    translationPermissionLabel: "Authorization:",

    // 统计信息
    views: "Views"
} as const;