export const article = {
    // 按钮和操作
    publish: "发布作品",
    update: "更新作品",
    edit: "编辑",
    delete: "删除",
    like: "点赞",
    unlike: "取消点赞",
    previewInNewWindow: "新窗口预览",
    previewMode: "作品预览模式",
    previewModeDesc: "这不是最终发布的版本",
    previewHint: "这是作品预览，返回编辑器继续编辑或发布作品",

    // 表单字段
    title: "标题",
    content: "内容",
    summary: "摘要",
    coverImage: "封面图片",
    tags: "标签",
    warningText: "警告文本",

    // 占位符
    titlePlaceholder: "请输入标题",
    summaryPlaceholder: "请输入摘要（可选）",
    contentPlaceholder: "开始编写...",
    tagsPlaceholder: "输入标签并按回车添加（最多5个）",
    warningPlaceholder: "请输入警告提示",

    // 设置
    settings: "作品设置",
    visibility: "可见性",
    visibilityPublic: "公开",
    visibilityLoggedIn: "仅登录用户可见",
    visibilityPublicDesc: "所有人可见",
    visibilityLoggedInDesc: "仅登录用户可见",
    contentWarning: "添加内容警告",
    visibilityQuestion: "谁可以看到这篇作品？",

    // 内容类型
    contentType: "内容类型",
    contentTypeQuestion: "请选择内容类型",
    contentTypeOriginal: "首发原创",
    contentTypeDistribution: "分发内容",
    contentTypeTranslation: "翻译内容",
    contentTypeOriginalDesc: "平台首次发布的原创内容",
    contentTypeDistributionDesc: "已在其他平台发布的内容分发",
    contentTypeTranslationDesc: "翻译自其他语言的内容",

    // 翻译授权
    translationAuth: "翻译授权",
    translationAuthRequired: "翻译内容需要提供授权说明",
    translationAuthPlaceholder: "请说明翻译授权来源，如：已获得原作者授权、符合合理使用、公共领域等",
    originalAuthor: "原作者",
    originalAuthorPlaceholder: "请输入原作者姓名",
    originalSource: "原文来源",
    originalSourcePlaceholder: "请输入原文链接或来源",
    translationPermission: "授权说明",
    translationPermissionPlaceholder: "请详细说明翻译授权情况",

    // 创作激励选择
    incentiveChoice: "创作激励",
    applyForIncentive: "申请创作激励",
    noIncentiveMode: "自由创作模式",
    incentiveChoiceQuestion: "您希望申请创作激励吗？",
    applyForIncentiveDesc: "享受创作激励，遵守相应协议条款",
    noIncentiveModeDesc: "放弃创作激励，享受更宽松的协议条款",
    incentiveAgreementNote: "选择申请激励将适用激励协议条款，包括删除限制等",
    freeAgreementNote: "选择自由模式将不享受激励，但协议更宽松",

    // 编辑限制提示
    incentiveContentTypeRestriction: "已申请创作激励的作品无法修改内容类型",
    incentiveChoiceRestriction: "已申请创作激励的作品无法修改激励申请状态",
    incentiveTranslationRestriction: "已申请创作激励的作品无法修改翻译授权信息",

    // 状态和消息
    submitting: "提交中...",
    publishSuccess: "作品发布成功",
    updateSuccess: "作品更新成功",
    publishSuccessDesc: "您的作品已发布",
    updateSuccessDesc: "您的作品已更新",
    operationFailed: "操作失败",
    retryLater: "请稍后重试",
    coverImageUploadFailed: "封面图片上传失败",

    // 验证消息
    titleMinLength: "标题最少5个字符，请增加更多内容",
    titleMaxLength: "标题最多30个字符，请简化标题",
    titleRequired: "请输入标题",
    contentMinLength: "内容最少300个字符，请添加更多内容",
    contentRequired: "请输入内容",
    summaryMaxLength: "摘要最多200个字符",
    warningMaxLength: "警告提示最多50个字符",
    warningRequired: "请输入警告提示",
    tagMaxLength: "标签最多20个字符",
    tagMaxCount: "最多添加5个标签",
    tagLimitReached: "标签数量限制",

    // 其他
    coverImageUpload: "点击上传封面图片",
    coverImagePreview: "封面预览",
    showContent: "显示内容",
    contentWarningTitle: "内容警告",
    contentWarningDefault: "此内容可能包含敏感信息",
    onlyLoggedInVisible: "仅登录用户可见",

    // 删除确认
    deleteConfirm: "确认删除",
    deleteConfirmDesc: "删除后无法恢复，确定要删除这篇作品吗？",
    deleteSuccess: "删除成功",
    deleteSuccessDesc: "作品已成功删除",
    deleteFailed: "删除失败",
    deleteIncentiveRestriction: "无法删除",
    deleteIncentiveRestrictionDesc: "申请了创作激励的作品在发布90天内无法删除，这是为了保证激励机制的公平性。",

    // 权限
    noEditPermission: "无权编辑",
    noEditPermissionDesc: "您只能编辑自己的作品",
    loginRequired: "请先登录",
    loginRequiredDesc: "登录后才能点赞作品",

    // 创作者协议
    creatorAgreement: "创作者协议",
    agreementTitle: "Queerecho 内容创作协议",
    agreeToTerms: "我已阅读并同意创作者协议",
    agreeToTermsPrefix: "我已阅读并同意",
    mustAgreeToTerms: "发布前需要同意创作者协议",

    // 协议类型
    originalAgreementTitle: "Queerecho 首发原创协议",
    distributionAgreementTitle: "Queerecho 内容分发协议",
    translationAgreementTitle: "Queerecho 翻译内容协议",
    incentiveAgreementTitle: "Queerecho 创作激励协议",
    freeAgreementTitle: "Queerecho 自由创作协议",
    viewFullAgreement: "查看完整协议",
    closeAgreement: "关闭协议",

    // 翻译信息显示
    translationInfo: "翻译授权信息",
    originalAuthorLabel: "原作者：",
    originalSourceLabel: "原文来源：",
    translationPermissionLabel: "翻译授权：",

    // 统计信息
    views: "浏览量",

    // 创作激励标识
    incentiveProgramBadge: "创作激励计划"
} as const;